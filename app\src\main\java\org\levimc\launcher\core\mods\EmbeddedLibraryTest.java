package org.levimc.launcher.core.mods;

import android.content.Context;
import android.util.Log;

public class EmbeddedLibraryTest {
    private static final String TAG = "EmbeddedLibraryTest";

    public static void testEmbeddedLibraryLoading(Context context) {
        try {
            // Load CrestHelper first (independent library)
            System.loadLibrary("CrestHelper");
            Log.i(TAG, "Successfully loaded libCrestHelper.so from APK");

            // Load DynamicLights (depends on CrestHelper)
            System.loadLibrary("DynamicLights");
            Log.i(TAG, "Successfully loaded libDynamicLights.so from APK");

        } catch (UnsatisfiedLinkError e) {
            Log.e(TAG, "Failed to load embedded libraries: " + e.getMessage());
        }
    }
}
