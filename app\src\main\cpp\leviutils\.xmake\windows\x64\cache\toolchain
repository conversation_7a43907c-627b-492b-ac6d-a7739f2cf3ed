{
    ["envs_arch_arm64-v8a_plat_android"] = {
        __global = true,
        plat = "android",
        arch = "arm64-v8a",
        __checked = true
    },
    ["tool_target_leviutils_android_arm64-v8a_sh"] = {
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolname = "clangxx",
        toolchain_info = {
            name = "ndk",
            arch = "arm64-v8a",
            cachekey = "ndk_arch_arm64-v8a_plat_android",
            plat = "android"
        }
    },
    ["ndk_arch_arm64-v8a_plat_android"] = {
        ndk_sdkver = "21",
        arch = "arm64-v8a",
        __global = true,
        __checked = true,
        bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
        plat = "android",
        ndk_sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
        cross = "aarch64-linux-android-",
        ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
        ndkver = 25,
        llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]]
    },
    ["rust_arch_arm64-v8a_plat_android"] = {
        __global = true,
        plat = "android",
        arch = "arm64-v8a",
        __checked = true
    },
    ["tool_target_leviutils_android_arm64-v8a_cxx"] = {
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolname = "clangxx",
        toolchain_info = {
            name = "ndk",
            arch = "arm64-v8a",
            cachekey = "ndk_arch_arm64-v8a_plat_android",
            plat = "android"
        }
    }
}