{
    ["ndk_arch_armeabi-v7a_plat_android"] = {
        ndk_sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
        llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
        bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
        __checked = true,
        __global = true,
        ndk_sdkver = "21",
        arch = "armeabi-v7a",
        cross = "arm-linux-androideabi-",
        ndkver = 25,
        ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
        plat = "android"
    },
    ["rust_arch_armeabi-v7a_plat_android"] = {
        plat = "android",
        __checked = true,
        arch = "armeabi-v7a",
        __global = true
    },
    ["tool_target_leviutils_android_armeabi-v7a_cxx"] = {
        toolname = "clangxx",
        toolchain_info = {
            plat = "android",
            name = "ndk",
            arch = "armeabi-v7a",
            cachekey = "ndk_arch_armeabi-v7a_plat_android"
        },
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["envs_arch_armeabi-v7a_plat_android"] = {
        plat = "android",
        __checked = true,
        arch = "armeabi-v7a",
        __global = true
    },
    ["tool_target_leviutils_android_armeabi-v7a_sh"] = {
        toolname = "clangxx",
        toolchain_info = {
            plat = "android",
            name = "ndk",
            arch = "armeabi-v7a",
            cachekey = "ndk_arch_armeabi-v7a_plat_android"
        },
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    }
}