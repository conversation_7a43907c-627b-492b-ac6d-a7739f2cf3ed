package org.levimc.launcher.core.mods;

import android.annotation.SuppressLint;
import android.content.Context;

import org.levimc.launcher.util.Logger;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;

public class ModNativeLoader {
    private static final String[] EMBEDDED_LIBS = {"libCrestHelper.so", "libDynamicLights.so"};

    @SuppressLint("UnsafeDynamicallyLoadedCode")
    public static void loadEnabledSoMods(ModManager modManager, File cacheDir, Context context) {
        loadEmbeddedLibraries(context);

        List<Mod> mods = modManager.getMods();
        for (Mod mod : mods) {
            if (!mod.isEnabled()) continue;

            String fileName = mod.getFileName();
            if (isEmbeddedLibrary(fileName)) {
                try {
                    String libName = fileName.substring(3, fileName.length() - 3);
                    System.loadLibrary(libName);
                    Logger.get().info("Loaded embedded so: " + fileName);
                } catch (UnsatisfiedLinkError e) {
                    Logger.get().error("Can't load embedded " + fileName + ": " + e.getMessage());
                }
            } else {
                File src = new File(modManager.getCurrentVersion().modsDir, fileName);
                File dir = new File(cacheDir, "mods");
                if (!dir.exists()) dir.mkdirs();
                File dst = new File(dir, fileName);
                try {
                    copyFile(src, dst);
                    System.load(dst.getAbsolutePath());
                    Logger.get().info("Loaded so: " + dst.getName());
                } catch (IOException | UnsatisfiedLinkError e) {
                    Logger.get().error("Can't load " + src.getName() + ": " + e.getMessage());
                }
            }
        }
    }

    private static boolean isEmbeddedLibrary(String fileName) {
        for (String embeddedLib : EMBEDDED_LIBS) {
            if (embeddedLib.equals(fileName)) {
                return true;
            }
        }
        return false;
    }

    private static void loadEmbeddedLibraries(Context context) {
        try {
            System.loadLibrary("CrestHelper");
            Logger.get().info("Pre-loaded libCrestHelper.so");
        } catch (UnsatisfiedLinkError e) {
            Logger.get().error("Failed to pre-load libCrestHelper.so: " + e.getMessage());
        }
    }

    private static void copyFile(File src, File dst) throws IOException {
        try (InputStream in = new FileInputStream(src);
             OutputStream out = new FileOutputStream(dst)) {
            byte[] buf = new byte[8192];
            int len;
            while ((len = in.read(buf)) > 0) {
                out.write(buf, 0, len);
            }
        }
    }
}
