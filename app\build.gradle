plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    id 'org.tboox.gradle-xmake-plugin' version '1.2.3'
}

def getGitTag() {
    def tag = "unknown"
    try {
        def stdout = new ByteArrayOutputStream()
        exec {
            commandLine 'git', 'describe', '--tags', '--abbrev=0'
            standardOutput = stdout
        }
        tag = stdout.toString().trim()
        if (tag.startsWith("v")) {
            tag = tag.substring(1)
        }
    } catch (ignored) {
    }
    return tag
}

static def getVersionCodeByTimestamp() {
    def baseDate = Date.parse("yyyy-MM-dd HH:mm:ss", "2025-01-01 00:00:00")
    def now = new Date()
    def diff = (now.time - baseDate.time) / 1000
    return diff.toInteger()
}

android {
    namespace 'org.levimc.launcher'
    compileSdk 35
    defaultConfig {
        applicationId "org.levimc.launcher"
        minSdk 24
        targetSdk 35
        versionCode getVersionCodeByTimestamp()
        versionName getGitTag()
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        ndk {
            //noinspection ChromeOsAbiSupport
            abiFilters 'arm64-v8a'
        }
        xmake {
            abiFilters 'arm64-v8a'
        }
    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['src/main/jniLibs']
        }
    }

    buildFeatures {
        viewBinding true
    }
    buildFeatures {
        prefab true
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }

    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_21
        targetCompatibility JavaVersion.VERSION_21
    }
    externalNativeBuild {
        xmake {
            path "src/main/cpp/leviutils/xmake.lua"
            logLevel "verbose"
            buildMode "release"
        }
    }
    ndkVersion "25.2.9519653"
}

dependencies {
    implementation project(':preloader')
    implementation libs.core.splashscreen
    implementation libs.apk.parser
    implementation libs.appcompat
    implementation libs.constraintlayout
    implementation libs.material
    implementation libs.gson
    implementation libs.okhttp3.okhttp
    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core
}
