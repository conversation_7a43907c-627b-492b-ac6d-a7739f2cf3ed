{
    ["find_program_ndk_arch_arm64-v8a_plat_android_checktoolsh"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_program_ndk_arch_arm64-v8a_plat_android_checktoolcxx"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-MM"] = true,
            ["-fsanitize-thread-atomics"] = true,
            ["-v"] = true,
            ["-fmodules-validate-system-headers"] = true,
            ["-ffixed-d2"] = true,
            ["-fstrict-float-cast-overflow"] = true,
            ["--verify-debug-info"] = true,
            ["-fsanitize-address-use-odr-indicator"] = true,
            ["-fprebuilt-implicit-modules"] = true,
            ["-fobjc-encode-cxx-class-template-spec"] = true,
            ["-fno-use-init-array"] = true,
            ["-U"] = true,
            ["-mabicalls"] = true,
            ["-fdebug-ranges-base-address"] = true,
            ["-fxl-pragma-pack"] = true,
            ["-fgpu-allow-device-init"] = true,
            ["-objcmt-migrate-ns-macros"] = true,
            ["-meabi"] = true,
            ["-relocatable-pch"] = true,
            ["-fmodules-strict-decluse"] = true,
            ["-mskip-rax-setup"] = true,
            ["-fno-sanitize-trap"] = true,
            ["-fstack-protector-all"] = true,
            ["-fforce-emit-vtables"] = true,
            ["--cuda-device-only"] = true,
            ["-malign-double"] = true,
            ["-munaligned-access"] = true,
            ["-fno-sanitize-address-outline-instrumentation"] = true,
            ["-help"] = true,
            ["-fdirect-access-external-data"] = true,
            ["-x"] = true,
            ["-gno-embed-source"] = true,
            ["-nogpulib"] = true,
            ["-mno-ms-bitfields"] = true,
            ["-fno-threadsafe-statics"] = true,
            ["-fapple-kext"] = true,
            ["-Xanalyzer"] = true,
            ["-cl-kernel-arg-info"] = true,
            ["-mqdsp6-compat"] = true,
            ["-pthread"] = true,
            ["-imacros"] = true,
            ["-fno-sanitize-cfi-canonical-jump-tables"] = true,
            ["-fno-crash-diagnostics"] = true,
            ["-w"] = true,
            ["-fno-sanitize-memory-track-origins"] = true,
            ["-fnew-infallible"] = true,
            ["-fpseudo-probe-for-profiling"] = true,
            ["-fmath-errno"] = true,
            ["-fhip-new-launch-api"] = true,
            ["-fno-legacy-pass-manager"] = true,
            ["-gdwarf32"] = true,
            ["-Xcuda-ptxas"] = true,
            ["-mfentry"] = true,
            ["-fno-rtti"] = true,
            ["-mcumode"] = true,
            ["-momit-leaf-frame-pointer"] = true,
            ["-fno-elide-type"] = true,
            ["-fropi"] = true,
            ["-flegacy-pass-manager"] = true,
            ["-fno-addrsig"] = true,
            ["-fpcc-struct-return"] = true,
            ["-cl-denorms-are-zero"] = true,
            ["-ffixed-x10"] = true,
            ["-fexperimental-new-constant-interpreter"] = true,
            ["-femulated-tls"] = true,
            ["-pipe"] = true,
            ["-objcmt-migrate-protocol-conformance"] = true,
            ["-fno-signed-zeros"] = true,
            ["-faapcs-bitfield-width"] = true,
            ["-mnvj"] = true,
            ["-mrelax-all"] = true,
            ["-fno-standalone-debug"] = true,
            ["-fno-fixed-point"] = true,
            ["-ffixed-x14"] = true,
            ["-fno-access-control"] = true,
            ["-fobjc-disable-direct-methods-for-testing"] = true,
            ["-ffixed-x30"] = true,
            ["-mno-crc"] = true,
            ["-mno-cumode"] = true,
            ["-iprefix"] = true,
            ["-fdeclspec"] = true,
            ["-pg"] = true,
            ["-fapplication-extension"] = true,
            ["-fno-strict-return"] = true,
            ["-mno-embedded-data"] = true,
            ["-fsplit-stack"] = true,
            ["-gdwarf-2"] = true,
            ["-fno-common"] = true,
            ["-fno-sanitize-cfi-cross-dso"] = true,
            ["-emit-ast"] = true,
            ["-fno-stack-clash-protection"] = true,
            ["-fvisibility-global-new-delete-hidden"] = true,
            ["-mbackchain"] = true,
            ["-mlong-double-80"] = true,
            ["-ffixed-x12"] = true,
            ["-fasync-exceptions"] = true,
            ["-fdiagnostics-absolute-paths"] = true,
            ["-mno-code-object-v3"] = true,
            ["-fborland-extensions"] = true,
            ["-time"] = true,
            ["-fms-extensions"] = true,
            ["-fno-sanitize-thread-memory-access"] = true,
            ["-objcmt-migrate-readonly-property"] = true,
            ["-fmodules-search-all"] = true,
            ["-fprotect-parens"] = true,
            ["-mcrc"] = true,
            ["-fno-discard-value-names"] = true,
            ["-fjump-tables"] = true,
            ["-cxx-isystem"] = true,
            ["-ffixed-a1"] = true,
            ["-fno-force-enable-int128"] = true,
            ["-Qunused-arguments"] = true,
            ["-ffixed-x5"] = true,
            ["-dI"] = true,
            ["-fno-trigraphs"] = true,
            ["-msave-restore"] = true,
            ["-membedded-data"] = true,
            ["-fbuiltin-module-map"] = true,
            ["-gline-tables-only"] = true,
            ["-arch"] = true,
            ["-fwhole-program-vtables"] = true,
            ["-gdwarf-3"] = true,
            ["-fsanitize-address-use-after-scope"] = true,
            ["-fsanitize-address-poison-custom-array-cookie"] = true,
            ["-fno-openmp-extensions"] = true,
            ["-fno-digraphs"] = true,
            ["-fmodules-disable-diagnostic-validation"] = true,
            ["-fapprox-func"] = true,
            ["-fno-pch-codegen"] = true,
            ["-MQ"] = true,
            ["-iquote"] = true,
            ["-fsanitize-memory-track-origins"] = true,
            ["-ffixed-x1"] = true,
            ["-fimplicit-module-maps"] = true,
            ["-objcmt-migrate-readwrite-property"] = true,
            ["-fdiagnostics-print-source-range-info"] = true,
            ["-fno-sanitize-ignorelist"] = true,
            ["-mmemops"] = true,
            ["-fprofile-instr-generate"] = true,
            ["-fno-sanitize-stats"] = true,
            ["-ffixed-x3"] = true,
            ["-fcuda-short-ptr"] = true,
            ["-dependency-file"] = true,
            ["-fdata-sections"] = true,
            ["-ffixed-x17"] = true,
            ["-fintegrated-cc1"] = true,
            ["-fno-coverage-mapping"] = true,
            ["-fregister-global-dtors-with-atexit"] = true,
            ["-emit-interface-stubs"] = true,
            ["-fconvergent-functions"] = true,
            ["-fcxx-exceptions"] = true,
            ["-mseses"] = true,
            ["-ffixed-x6"] = true,
            ["-working-directory"] = true,
            ["-finline-hint-functions"] = true,
            ["-fno-split-stack"] = true,
            ["-objcmt-allowlist-dir-path"] = true,
            ["-fpch-instantiate-templates"] = true,
            ["-fno-sanitize-hwaddress-experimental-aliasing"] = true,
            ["-index-header-map"] = true,
            ["-fno-lto"] = true,
            ["-enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang"] = true,
            ["-fopenmp-target-new-runtime"] = true,
            ["-fvalidate-ast-input-files-content"] = true,
            ["-mno-unaligned-access"] = true,
            ["-iwithprefix"] = true,
            ["-fno-visibility-inlines-hidden-static-local-var"] = true,
            ["-fsanitize-trap"] = true,
            ["-Xopenmp-target"] = true,
            ["-fno-rtti-data"] = true,
            ["-ffixed-x31"] = true,
            ["-fhip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-dsym-dir"] = true,
            ["-mtls-direct-seg-refs"] = true,
            ["-ffixed-x4"] = true,
            ["-fpch-codegen"] = true,
            ["-fdiagnostics-show-option"] = true,
            ["-ffixed-d0"] = true,
            ["-objcmt-atomic-property"] = true,
            ["-fgpu-sanitize"] = true,
            ["-fno-profile-instr-use"] = true,
            ["-nostdinc"] = true,
            ["-fembed-bitcode-marker"] = true,
            ["-funique-internal-linkage-names"] = true,
            ["-fslp-vectorize"] = true,
            ["-MT"] = true,
            ["-b"] = true,
            ["-fdiagnostics-show-hotness"] = true,
            ["-MD"] = true,
            ["-ffixed-x9"] = true,
            ["-fforce-enable-int128"] = true,
            ["-fmodules-validate-input-files-content"] = true,
            ["-fvisibility-from-dllstorageclass"] = true,
            ["-ffixed-d4"] = true,
            ["-print-runtime-dir"] = true,
            ["-B"] = true,
            ["-fno-show-column"] = true,
            ["-mrestrict-it"] = true,
            ["-fwritable-strings"] = true,
            ["-ffinite-loops"] = true,
            ["-ffixed-d1"] = true,
            ["-mfp64"] = true,
            ["-mignore-xcoff-visibility"] = true,
            ["-menable-experimental-extensions"] = true,
            ["-mfp32"] = true,
            ["-fintegrated-as"] = true,
            ["-mno-lvi-cfi"] = true,
            ["-fno-sanitize-memory-use-after-dtor"] = true,
            ["-isysroot"] = true,
            ["-mcode-object-v3"] = true,
            ["-MF"] = true,
            ["-C"] = true,
            ["-finstrument-function-entry-bare"] = true,
            ["-fno-elide-constructors"] = true,
            ["-fcoroutines-ts"] = true,
            ["-finstrument-functions-after-inlining"] = true,
            ["-fno-cxx-modules"] = true,
            ["-fallow-editor-placeholders"] = true,
            ["--cuda-host-only"] = true,
            ["-fms-compatibility"] = true,
            ["-fcxx-modules"] = true,
            ["-fkeep-static-consts"] = true,
            ["-fsjlj-exceptions"] = true,
            ["-MP"] = true,
            ["-mstackrealign"] = true,
            ["-gno-inline-line-tables"] = true,
            ["-trigraphs"] = true,
            ["-D"] = true,
            ["-objcmt-migrate-all"] = true,
            ["-ffixed-x22"] = true,
            ["-c"] = true,
            ["-fdelete-null-pointer-checks"] = true,
            ["-fcall-saved-x12"] = true,
            ["-mfix-cortex-a53-835769"] = true,
            ["-fdollars-in-identifiers"] = true,
            ["-mnocrc"] = true,
            ["-forder-file-instrumentation"] = true,
            ["-fno-hip-new-launch-api"] = true,
            ["-E"] = true,
            ["-funique-basic-block-section-names"] = true,
            ["-mno-save-restore"] = true,
            ["-fno-complete-member-pointers"] = true,
            ["--emit-static-lib"] = true,
            ["-MMD"] = true,
            ["-fno-autolink"] = true,
            ["-fcuda-approx-transcendentals"] = true,
            ["-fdebug-info-for-profiling"] = true,
            ["-ffixed-d5"] = true,
            ["-mhvx-ieee-fp"] = true,
            ["--cuda-compile-host-device"] = true,
            ["-mno-movt"] = true,
            ["-gcodeview"] = true,
            ["-mno-tgsplit"] = true,
            ["-fno-gnu-inline-asm"] = true,
            ["-Xcuda-fatbinary"] = true,
            ["-fxray-always-emit-customevents"] = true,
            ["-fexceptions"] = true,
            ["-mno-stack-arg-probe"] = true,
            ["-fgpu-rdc"] = true,
            ["-fopenmp-target-debug"] = true,
            ["-fno-memory-profile"] = true,
            ["-ffixed-a6"] = true,
            ["-objcmt-returns-innerpointer-property"] = true,
            ["-mtgsplit"] = true,
            ["-fsanitize-cfi-cross-dso"] = true,
            ["-mno-long-calls"] = true,
            ["-ffixed-d3"] = true,
            ["-fno-debug-macro"] = true,
            ["-fexperimental-strict-floating-point"] = true,
            ["-mthread-model"] = true,
            ["-mstack-arg-probe"] = true,
            ["-serialize-diagnostics"] = true,
            ["-mno-wavefrontsize64"] = true,
            ["-ffixed-x19"] = true,
            ["-nogpuinc"] = true,
            ["-objcmt-migrate-annotation"] = true,
            ["-print-multiarch"] = true,
            ["-Xlinker"] = true,
            ["-fverbose-asm"] = true,
            ["-mno-restrict-it"] = true,
            ["-H"] = true,
            ["-mbranches-within-32B-boundaries"] = true,
            ["-ffixed-x8"] = true,
            ["-fno-signed-char"] = true,
            ["-fno-cuda-approx-transcendentals"] = true,
            ["-cl-no-signed-zeros"] = true,
            ["-fcall-saved-x11"] = true,
            ["-g"] = true,
            ["-cl-finite-math-only"] = true,
            ["-fno-sanitize-memory-param-retval"] = true,
            ["-gembed-source"] = true,
            ["-fno-gpu-defer-diag"] = true,
            ["-Tdata"] = true,
            ["-ivfsoverlay"] = true,
            ["-fno-diagnostics-fixit-info"] = true,
            ["-gdwarf"] = true,
            ["-Xpreprocessor"] = true,
            ["-print-supported-cpus"] = true,
            ["-print-resource-dir"] = true,
            ["-freroll-loops"] = true,
            ["-fno-aapcs-bitfield-width"] = true,
            ["-ffixed-x18"] = true,
            ["-mno-global-merge"] = true,
            ["-include-pch"] = true,
            ["-arcmt-migrate-report-output"] = true,
            ["--start-no-unused-arguments"] = true,
            ["-ffine-grained-bitfield-accesses"] = true,
            ["-idirafter"] = true,
            ["-fblocks"] = true,
            ["-I"] = true,
            ["-print-ivar-layout"] = true,
            ["-mno-abicalls"] = true,
            ["-mno-hvx"] = true,
            ["-cl-mad-enable"] = true,
            ["-fno-spell-checking"] = true,
            ["-fopenmp-extensions"] = true,
            ["-mno-execute-only"] = true,
            ["-module-file-info"] = true,
            ["-ffixed-x25"] = true,
            ["-fno-new-infallible"] = true,
            ["-fno-dollars-in-identifiers"] = true,
            ["-fno-double-square-bracket-attributes"] = true,
            ["-fcommon"] = true,
            ["-dD"] = true,
            ["-ffixed-x16"] = true,
            ["-fsanitize-address-globals-dead-stripping"] = true,
            ["-fvisibility-inlines-hidden-static-local-var"] = true,
            ["-fno-integrated-as"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-fgnu-runtime"] = true,
            ["--gpu-bundle-output"] = true,
            ["-fno-short-wchar"] = true,
            ["-fxray-always-emit-typedevents"] = true,
            ["-cl-unsafe-math-optimizations"] = true,
            ["-fsanitize-thread-memory-access"] = true,
            ["-cl-opt-disable"] = true,
            ["-mno-msa"] = true,
            ["-ffixed-x21"] = true,
            ["-fno-xray-function-index"] = true,
            ["-fno-split-machine-functions"] = true,
            ["-ffixed-x13"] = true,
            ["-mno-seses"] = true,
            ["-mno-tls-direct-seg-refs"] = true,
            ["-z"] = true,
            ["-fsycl"] = true,
            ["-fno-pch-debuginfo"] = true,
            ["-cl-strict-aliasing"] = true,
            ["-fstack-size-section"] = true,
            ["-fcolor-diagnostics"] = true,
            ["-fno-exceptions"] = true,
            ["-ffixed-point"] = true,
            ["-fno-sanitize-address-use-odr-indicator"] = true,
            ["--precompile"] = true,
            ["-fignore-exceptions"] = true,
            ["-fno-unroll-loops"] = true,
            ["-isystem"] = true,
            ["-emit-llvm"] = true,
            ["-fpch-debuginfo"] = true,
            ["-fno-gpu-allow-device-init"] = true,
            ["-ffixed-r9"] = true,
            ["-fno-builtin"] = true,
            ["-fno-delete-null-pointer-checks"] = true,
            ["-undef"] = true,
            ["-gcodeview-ghash"] = true,
            ["-fno-profile-generate"] = true,
            ["-mrelax"] = true,
            ["-fno-zero-initialized-in-bss"] = true,
            ["-fobjc-arc"] = true,
            ["-munsafe-fp-atomics"] = true,
            ["-ffast-math"] = true,
            ["-finstrument-functions"] = true,
            ["-mno-packets"] = true,
            ["-moutline"] = true,
            ["-fmodules-ts"] = true,
            ["-fshort-enums"] = true,
            ["-fprofile-generate"] = true,
            ["-fno-pseudo-probe-for-profiling"] = true,
            ["-fno-preserve-as-comments"] = true,
            ["-fopenmp"] = true,
            ["-ffixed-x11"] = true,
            ["-fno-operator-names"] = true,
            ["-fxray-link-deps"] = true,
            ["-MG"] = true,
            ["-mms-bitfields"] = true,
            ["-fno-offload-lto"] = true,
            ["-Qn"] = true,
            ["-arcmt-migrate-emit-errors"] = true,
            ["-fno-constant-cfstrings"] = true,
            ["-mno-gpopt"] = true,
            ["-mhvx-qfloat"] = true,
            ["-fembed-bitcode"] = true,
            ["-ffixed-a4"] = true,
            ["-fstrict-vtable-pointers"] = true,
            ["-L"] = true,
            ["-Tbss"] = true,
            ["-fprofile-sample-accurate"] = true,
            ["-fno-rtlib-add-rpath"] = true,
            ["-fshort-wchar"] = true,
            ["-include"] = true,
            ["-fdiagnostics-show-template-tree"] = true,
            ["-mnop-mcount"] = true,
            ["-objcmt-migrate-designated-init"] = true,
            ["-fsigned-char"] = true,
            ["-print-effective-triple"] = true,
            ["-Xclang"] = true,
            ["-rewrite-objc"] = true,
            ["-iwithprefixbefore"] = true,
            ["-fno-profile-instr-generate"] = true,
            ["-fcall-saved-x10"] = true,
            ["--no-cuda-version-check"] = true,
            ["-fxray-instrument"] = true,
            ["-dM"] = true,
            ["-traditional-cpp"] = true,
            ["-mno-fix-cmse-cve-2021-35465"] = true,
            ["-fno-sanitize-address-poison-custom-array-cookie"] = true,
            ["-fmerge-all-constants"] = true,
            ["-module-dependency-dir"] = true,
            ["-fdwarf-exceptions"] = true,
            ["-fcall-saved-x15"] = true,
            ["-mno-hvx-ieee-fp"] = true,
            ["-gline-directives-only"] = true,
            ["-M"] = true,
            ["--help-hidden"] = true,
            ["-cl-no-stdinc"] = true,
            ["-fno-global-isel"] = true,
            ["-faligned-allocation"] = true,
            ["-ffixed-x7"] = true,
            ["-print-search-dirs"] = true,
            ["-mamdgpu-ieee"] = true,
            ["-ffixed-a2"] = true,
            ["-mpackets"] = true,
            ["-fobjc-weak"] = true,
            ["-mno-relax"] = true,
            ["-fdelayed-template-parsing"] = true,
            ["-objcmt-ns-nonatomic-iosonly"] = true,
            ["-cl-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fseh-exceptions"] = true,
            ["-moutline-atomics"] = true,
            ["-mmark-bti-property"] = true,
            ["-mno-bti-at-return-twice"] = true,
            ["-fsanitize-memory-param-retval"] = true,
            ["-fstrict-enums"] = true,
            ["-faapcs-bitfield-load"] = true,
            ["-fsanitize-stats"] = true,
            ["-fmemory-profile"] = true,
            ["-ibuiltininc"] = true,
            ["-fsanitize-thread-func-entry-exit"] = true,
            ["-fno-objc-infer-related-result-type"] = true,
            ["-freg-struct-return"] = true,
            ["-fsplit-machine-functions"] = true,
            ["-static-openmp"] = true,
            ["-ffixed-x23"] = true,
            ["--cuda-path-ignore-env"] = true,
            ["-gmodules"] = true,
            ["-fsanitize-memory-use-after-dtor"] = true,
            ["-mno-outline-atomics"] = true,
            ["-ffunction-sections"] = true,
            ["-rpath"] = true,
            ["-fvectorize"] = true,
            ["-mno-implicit-float"] = true,
            ["-mlvi-cfi"] = true,
            ["-menable-unsafe-fp-math"] = true,
            ["-objcmt-migrate-instancetype"] = true,
            ["-fcall-saved-x8"] = true,
            ["-fno-keep-static-consts"] = true,
            ["-fcall-saved-x18"] = true,
            ["-ffixed-r19"] = true,
            ["-gdwarf-5"] = true,
            ["-mrecord-mcount"] = true,
            ["-ffixed-x24"] = true,
            ["-ffixed-x2"] = true,
            ["-mgpopt"] = true,
            ["-mpacked-stack"] = true,
            ["-fcall-saved-x9"] = true,
            ["--analyze"] = true,
            ["-fdigraphs"] = true,
            ["-fdiscard-value-names"] = true,
            ["-frelaxed-template-template-args"] = true,
            ["-fzvector"] = true,
            ["-fno-delayed-template-parsing"] = true,
            ["-fno-show-source-location"] = true,
            ["-faddrsig"] = true,
            ["-gdwarf-4"] = true,
            ["-freciprocal-math"] = true,
            ["-mlong-double-64"] = true,
            ["-iwithsysroot"] = true,
            ["-fno-temp-file"] = true,
            ["-save-temps"] = true,
            ["-fvisibility-ms-compat"] = true,
            ["-fstack-protector"] = true,
            ["-miamcu"] = true,
            ["-fsave-optimization-record"] = true,
            ["-msvr4-struct-return"] = true,
            ["-print-target-triple"] = true,
            ["-Ttext"] = true,
            ["-fsized-deallocation"] = true,
            ["-mibt-seal"] = true,
            ["-fno-strict-float-cast-overflow"] = true,
            ["-P"] = true,
            ["-maix-struct-return"] = true,
            ["-ffixed-d6"] = true,
            ["-ftrapv"] = true,
            ["-fstandalone-debug"] = true,
            ["-fno-stack-protector"] = true,
            ["-fmodules-validate-once-per-build-session"] = true,
            ["-objcmt-migrate-property"] = true,
            ["-mglobal-merge"] = true,
            ["-fcoverage-mapping"] = true,
            ["-ffixed-x26"] = true,
            ["--no-gpu-bundle-output"] = true,
            ["-gdwarf64"] = true,
            ["-o"] = true,
            ["-cl-uniform-work-group-size"] = true,
            ["-MV"] = true,
            ["-femit-all-decls"] = true,
            ["-fapple-link-rtlib"] = true,
            ["-ffixed-x28"] = true,
            ["--cuda-noopt-device-debug"] = true,
            ["-fglobal-isel"] = true,
            ["-fobjc-arc-exceptions"] = true,
            ["-fsplit-lto-unit"] = true,
            ["-frwpi"] = true,
            ["-mno-lvi-hardening"] = true,
            ["-objcmt-migrate-subscripting"] = true,
            ["-F"] = true,
            ["-fno-sycl"] = true,
            ["-mno-outline"] = true,
            ["-G"] = true,
            ["-fopenmp-simd"] = true,
            ["-static-libsan"] = true,
            ["-fno-integrated-cc1"] = true,
            ["-mnvs"] = true,
            ["-mno-nvj"] = true,
            ["-save-stats"] = true,
            ["--version"] = true,
            ["-fmodules-decluse"] = true,
            ["-fvirtual-function-elimination"] = true,
            ["-mmt"] = true,
            ["-ffixed-a3"] = true,
            ["-ffixed-d7"] = true,
            ["-fenable-matrix"] = true,
            ["-fno-use-cxa-atexit"] = true,
            ["-fgpu-flush-denormals-to-zero"] = true,
            ["-fcs-profile-generate"] = true,
            ["-flto"] = true,
            ["-mlvi-hardening"] = true,
            ["-mno-memops"] = true,
            ["-fapple-pragma-pack"] = true,
            ["-fdouble-square-bracket-attributes"] = true,
            ["--hip-link"] = true,
            ["-fforce-dwarf-frame"] = true,
            ["-Qy"] = true,
            ["-fmodules"] = true,
            ["-funroll-loops"] = true,
            ["-extract-api"] = true,
            ["-print-rocm-search-dirs"] = true,
            ["-fsanitize-hwaddress-experimental-aliasing"] = true,
            ["-fpascal-strings"] = true,
            ["-fno-plt"] = true,
            ["-mno-local-sdata"] = true,
            ["-objcmt-migrate-literals"] = true,
            ["-fsplit-dwarf-inlining"] = true,
            ["-mlong-calls"] = true,
            ["-Xassembler"] = true,
            ["-fdebug-macro"] = true,
            ["-mrtd"] = true,
            ["-fwasm-exceptions"] = true,
            ["-fno-fine-grained-bitfield-accesses"] = true,
            ["-pedantic"] = true,
            ["-mfix-cmse-cve-2021-35465"] = true,
            ["-ffixed-a5"] = true,
            ["-mwavefrontsize64"] = true,
            ["-objcmt-migrate-property-dot-syntax"] = true,
            ["-mhvx"] = true,
            ["-mincremental-linker-compatible"] = true,
            ["-fsystem-module"] = true,
            ["-Wdeprecated"] = true,
            ["-fno-hip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-MJ"] = true,
            ["-dependency-dot"] = true,
            ["-frtlib-add-rpath"] = true,
            ["-mno-nvs"] = true,
            ["-fms-hotpatch"] = true,
            ["-fno-direct-access-external-data"] = true,
            ["-fno-color-diagnostics"] = true,
            ["-fno-jump-tables"] = true,
            ["-msoft-float"] = true,
            ["-mlong-double-128"] = true,
            ["-I-"] = true,
            ["-fgnu-keywords"] = true,
            ["-fno-sanitize-address-use-after-scope"] = true,
            ["-CC"] = true,
            ["-mmadd4"] = true,
            ["-fpch-validate-input-files-content"] = true,
            ["-nobuiltininc"] = true,
            ["-fgnu89-inline"] = true,
            ["-mno-fix-cortex-a53-835769"] = true,
            ["-fansi-escape-codes"] = true,
            ["-rewrite-legacy-objc"] = true,
            ["-mmsa"] = true,
            ["-fsanitize-cfi-icall-generalize-pointers"] = true,
            ["-mlocal-sdata"] = true,
            ["-iframeworkwithsysroot"] = true,
            ["-nohipwrapperinc"] = true,
            ["-fsanitize-cfi-canonical-jump-tables"] = true,
            ["-emit-module"] = true,
            ["-ftrigraphs"] = true,
            ["-mgeneral-regs-only"] = true,
            ["-fsanitize-address-outline-instrumentation"] = true,
            ["-verify-pch"] = true,
            ["-fno-unique-section-names"] = true,
            ["-fgpu-defer-diag"] = true,
            ["-fstack-usage"] = true,
            ["-print-targets"] = true,
            ["-mno-mt"] = true,
            ["-mno-incremental-linker-compatible"] = true,
            ["-shared-libsan"] = true,
            ["-mextern-sdata"] = true,
            ["-cl-fast-relaxed-math"] = true,
            ["-mno-extern-sdata"] = true,
            ["-fcall-saved-x13"] = true,
            ["-mno-neg-immediates"] = true,
            ["-fno-sanitize-thread-func-entry-exit"] = true,
            ["-fuse-line-directives"] = true,
            ["-fstack-protector-strong"] = true,
            ["-fno-merge-all-constants"] = true,
            ["-ffixed-x27"] = true,
            ["-mno-madd4"] = true,
            ["-S"] = true,
            ["-mllvm"] = true,
            ["-ffreestanding"] = true,
            ["-ffixed-x29"] = true,
            ["-feliminate-unused-debug-types"] = true,
            ["-finline-functions"] = true,
            ["-mexecute-only"] = true,
            ["-mcmse"] = true,
            ["-fshow-skipped-includes"] = true,
            ["-fminimize-whitespace"] = true,
            ["-fno-declspec"] = true,
            ["--analyzer-output"] = true,
            ["-fstack-clash-protection"] = true,
            ["-fdebug-types-section"] = true,
            ["-fvisibility-inlines-hidden"] = true,
            ["-fno-eliminate-unused-debug-types"] = true,
            ["-fno-assume-sane-operator-new"] = true,
            ["-fmodules-user-build-path"] = true,
            ["-mno-hvx-qfloat"] = true,
            ["-fcall-saved-x14"] = true,
            ["-fdiagnostics-parseable-fixits"] = true,
            ["--end-no-unused-arguments"] = true,
            ["-foffload-lto"] = true,
            ["-fno-finite-loops"] = true,
            ["-fdiagnostics-show-note-include-stack"] = true,
            ["-fxray-ignore-loops"] = true,
            ["-fcomplete-member-pointers"] = true,
            ["-fcf-protection"] = true,
            ["-ffixed-a0"] = true,
            ["-ftime-trace"] = true,
            ["-ffixed-x20"] = true,
            ["-T"] = true,
            ["-fno-register-global-dtors-with-atexit"] = true,
            ["-emit-merged-ifs"] = true,
            ["-cl-single-precision-constant"] = true,
            ["-fobjc-exceptions"] = true,
            ["-isystem-after"] = true,
            ["--migrate"] = true,
            ["-ffixed-x15"] = true,
            ["-fno-sanitize-thread-atomics"] = true,
            ["--config"] = true
        }
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    },
    ["find_program_utils.binary.deplibs"] = {
        objdump = [[C:\msys64\usr\bin\objdump.exe]],
        ["llvm-objdump"] = false
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["--version"] = true,
            ["--dynamicbase"] = true,
            ["-l"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["-v"] = true,
            ["--no-whole-archive"] = true,
            ["--verbose"] = true,
            ["--no-dynamicbase"] = true,
            ["--enable-auto-import"] = true,
            ["--no-insert-timestamp"] = true,
            ["--Bdynamic"] = true,
            ["--disable-auto-import"] = true,
            ["--no-allow-multiple-definition"] = true,
            ["--no-demangle"] = true,
            ["--disable-no-seh"] = true,
            ["--kill-at"] = true,
            ["-dn"] = true,
            ["-o"] = true,
            ["--shared"] = true,
            ["--no-fatal-warnings"] = true,
            ["--export-all-symbols"] = true,
            ["--disable-dynamicbase"] = true,
            ["--help"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["--appcontainer"] = true,
            ["--Bstatic"] = true,
            ["--disable-high-entropy-va"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["--disable-tsaware"] = true,
            ["--tsaware"] = true,
            ["--allow-multiple-definition"] = true,
            ["--no-seh"] = true,
            ["-L"] = true,
            ["-static"] = true,
            ["-dy"] = true,
            ["--gc-sections"] = true,
            ["--disable-nxcompat"] = true,
            ["--exclude-all-symbols"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["--strip-debug"] = true,
            ["-S"] = true,
            ["--whole-archive"] = true,
            ["--fatal-warnings"] = true,
            ["--insert-timestamp"] = true,
            ["--nxcompat"] = true,
            ["--large-address-aware"] = true,
            ["--no-gc-sections"] = true,
            ["--high-entropy-va"] = true,
            ["--strip-all"] = true,
            ["-m"] = true,
            ["--demangle"] = true,
            ["-s"] = true
        }
    },
    ["find_program_ndk_arch_arm64-v8a_plat_android_checktoolld"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    find_programver = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = "14.0.7"
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
            ndkver = 25,
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
            cross = "aarch64-linux-android-",
            sdkver = "21",
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]]
        }
    },
    find_program = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["lib.detect.has_flags"] = {
        ["android_arm64-v8a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_sh__-shared -llog --target=aarch64-none-linux-android21 -nostdlib++ -llog --target=aarch64-none-linux-android21 -nostdlib++_-fPIC"] = true,
        ["android_arm64-v8a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\aarch64-linux-android -nostdinc++ -Qunused-arguments --target=aarch64-none-linux-android21_-fPIC"] = true,
        ["android_arm64-v8a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\aarch64-linux-android -nostdinc++ -Qunused-arguments --target=aarch64-none-linux-android21_-Oz"] = true,
        ["android_arm64-v8a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\aarch64-linux-android -nostdinc++ -Qunused-arguments --target=aarch64-none-linux-android21_-DNDEBUG"] = true,
        ["android_arm64-v8a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\aarch64-linux-android -nostdinc++ -Qunused-arguments --target=aarch64-none-linux-android21_-std=c++20"] = true,
        ["android_arm64-v8a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\aarch64-linux-android -nostdinc++ -Qunused-arguments --target=aarch64-none-linux-android21_-O3"] = true
    }
}