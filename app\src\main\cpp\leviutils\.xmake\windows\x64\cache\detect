{
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-l"] = true,
            ["--no-dynamicbase"] = true,
            ["--strip-debug"] = true,
            ["--no-allow-multiple-definition"] = true,
            ["--dynamicbase"] = true,
            ["--no-fatal-warnings"] = true,
            ["--whole-archive"] = true,
            ["-S"] = true,
            ["--appcontainer"] = true,
            ["--insert-timestamp"] = true,
            ["--kill-at"] = true,
            ["--Bdynamic"] = true,
            ["-dy"] = true,
            ["--enable-auto-import"] = true,
            ["--large-address-aware"] = true,
            ["--version"] = true,
            ["--no-whole-archive"] = true,
            ["--no-insert-timestamp"] = true,
            ["--verbose"] = true,
            ["-static"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["--disable-dynamicbase"] = true,
            ["--allow-multiple-definition"] = true,
            ["--Bstatic"] = true,
            ["--fatal-warnings"] = true,
            ["--disable-high-entropy-va"] = true,
            ["--disable-no-seh"] = true,
            ["--export-all-symbols"] = true,
            ["--disable-auto-import"] = true,
            ["--exclude-all-symbols"] = true,
            ["--no-gc-sections"] = true,
            ["-o"] = true,
            ["--gc-sections"] = true,
            ["--demangle"] = true,
            ["--strip-all"] = true,
            ["--nxcompat"] = true,
            ["-L"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["--disable-tsaware"] = true,
            ["--disable-nxcompat"] = true,
            ["-dn"] = true,
            ["--no-seh"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["--shared"] = true,
            ["-s"] = true,
            ["--no-demangle"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["-m"] = true,
            ["-v"] = true,
            ["--help"] = true,
            ["--high-entropy-va"] = true,
            ["--tsaware"] = true
        }
    },
    ["find_program_utils.binary.deplibs"] = {
        ["llvm-objdump"] = false,
        objdump = [[C:\msys64\usr\bin\objdump.exe]]
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolsh"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            sdkver = "21",
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
            cross = "arm-linux-androideabi-",
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
            ndkver = 25,
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]]
        }
    },
    find_programver = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = "14.0.7"
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolcxx"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-mfentry"] = true,
            ["-fxray-link-deps"] = true,
            ["-mextern-sdata"] = true,
            ["-mrelax"] = true,
            ["-fsplit-dwarf-inlining"] = true,
            ["-fxray-instrument"] = true,
            ["-fno-pseudo-probe-for-profiling"] = true,
            ["-ffixed-d4"] = true,
            ["-fforce-emit-vtables"] = true,
            ["-verify-pch"] = true,
            ["-fms-hotpatch"] = true,
            ["-meabi"] = true,
            ["-mfp32"] = true,
            ["-malign-double"] = true,
            ["-nogpuinc"] = true,
            ["-fvisibility-ms-compat"] = true,
            ["-fpch-codegen"] = true,
            ["-static-libsan"] = true,
            ["-enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang"] = true,
            ["-print-runtime-dir"] = true,
            ["-mno-tls-direct-seg-refs"] = true,
            ["-mlong-calls"] = true,
            ["-mno-crc"] = true,
            ["-fdebug-info-for-profiling"] = true,
            ["-print-targets"] = true,
            ["-trigraphs"] = true,
            ["-mcode-object-v3"] = true,
            ["-fno-rtti-data"] = true,
            ["-gline-directives-only"] = true,
            ["-ffixed-x13"] = true,
            ["-fdiagnostics-show-hotness"] = true,
            ["-objcmt-migrate-subscripting"] = true,
            ["-fno-standalone-debug"] = true,
            ["-mrelax-all"] = true,
            ["-fshort-wchar"] = true,
            ["-nostdinc"] = true,
            ["-fno-keep-static-consts"] = true,
            ["-fcall-saved-x10"] = true,
            ["-mnop-mcount"] = true,
            ["-fcxx-modules"] = true,
            ["--analyzer-output"] = true,
            ["-save-temps"] = true,
            ["-MD"] = true,
            ["-arcmt-migrate-emit-errors"] = true,
            ["-fgnu89-inline"] = true,
            ["-mno-movt"] = true,
            ["-fvisibility-inlines-hidden"] = true,
            ["-fno-new-infallible"] = true,
            ["-ffixed-x25"] = true,
            ["-mtgsplit"] = true,
            ["-funroll-loops"] = true,
            ["-mhvx-qfloat"] = true,
            ["-fexperimental-new-constant-interpreter"] = true,
            ["-fopenmp-target-new-runtime"] = true,
            ["--cuda-device-only"] = true,
            ["-fno-lto"] = true,
            ["-fno-trigraphs"] = true,
            ["-mms-bitfields"] = true,
            ["-fdiagnostics-parseable-fixits"] = true,
            ["-gcodeview"] = true,
            ["-mno-abicalls"] = true,
            ["-fno-sycl"] = true,
            ["-help"] = true,
            ["-fno-double-square-bracket-attributes"] = true,
            ["-mrecord-mcount"] = true,
            ["-objcmt-atomic-property"] = true,
            ["-fno-gpu-allow-device-init"] = true,
            ["-fmemory-profile"] = true,
            ["-fno-split-stack"] = true,
            ["-Xassembler"] = true,
            ["-fsanitize-stats"] = true,
            ["-M"] = true,
            ["-fkeep-static-consts"] = true,
            ["--gpu-bundle-output"] = true,
            ["-mno-cumode"] = true,
            ["-fno-access-control"] = true,
            ["-mglobal-merge"] = true,
            ["-fprofile-instr-generate"] = true,
            ["-fopenmp-simd"] = true,
            ["-MQ"] = true,
            ["-mno-outline"] = true,
            ["-fno-sanitize-address-poison-custom-array-cookie"] = true,
            ["-menable-experimental-extensions"] = true,
            ["-fsanitize-address-globals-dead-stripping"] = true,
            ["-fno-rtlib-add-rpath"] = true,
            ["-fno-pch-codegen"] = true,
            ["-mno-fix-cmse-cve-2021-35465"] = true,
            ["-fdebug-macro"] = true,
            ["-fgpu-defer-diag"] = true,
            ["-P"] = true,
            ["-objcmt-migrate-property-dot-syntax"] = true,
            ["-fdiagnostics-absolute-paths"] = true,
            ["-fmodules-user-build-path"] = true,
            ["-faapcs-bitfield-load"] = true,
            ["-nohipwrapperinc"] = true,
            ["-fno-signed-zeros"] = true,
            ["-fdata-sections"] = true,
            ["-feliminate-unused-debug-types"] = true,
            ["-cl-uniform-work-group-size"] = true,
            ["-emit-interface-stubs"] = true,
            ["-gdwarf-3"] = true,
            ["-undef"] = true,
            ["-objcmt-migrate-protocol-conformance"] = true,
            ["-S"] = true,
            ["-fcoroutines-ts"] = true,
            ["-isysroot"] = true,
            ["-fblocks"] = true,
            ["-mhvx"] = true,
            ["-mno-embedded-data"] = true,
            ["-mno-madd4"] = true,
            ["-fapple-kext"] = true,
            ["-fvisibility-from-dllstorageclass"] = true,
            ["-mbranches-within-32B-boundaries"] = true,
            ["-fstrict-float-cast-overflow"] = true,
            ["--emit-static-lib"] = true,
            ["-rewrite-legacy-objc"] = true,
            ["-fglobal-isel"] = true,
            ["-ffixed-x26"] = true,
            ["-fxray-always-emit-typedevents"] = true,
            ["-momit-leaf-frame-pointer"] = true,
            ["-arcmt-migrate-report-output"] = true,
            ["-mignore-xcoff-visibility"] = true,
            ["-mno-global-merge"] = true,
            ["-fcf-protection"] = true,
            ["-ftime-trace"] = true,
            ["-fapple-link-rtlib"] = true,
            ["-fprebuilt-implicit-modules"] = true,
            ["-Xopenmp-target"] = true,
            ["-fobjc-encode-cxx-class-template-spec"] = true,
            ["-fsanitize-thread-func-entry-exit"] = true,
            ["-gdwarf"] = true,
            ["-fno-integrated-cc1"] = true,
            ["-fno-sanitize-ignorelist"] = true,
            ["-ffixed-a4"] = true,
            ["-mmark-bti-property"] = true,
            ["-fenable-matrix"] = true,
            ["-fdwarf-exceptions"] = true,
            ["-mno-fix-cortex-a53-835769"] = true,
            ["-mno-ms-bitfields"] = true,
            ["-mno-bti-at-return-twice"] = true,
            ["-mbackchain"] = true,
            ["-T"] = true,
            ["-fvisibility-global-new-delete-hidden"] = true,
            ["-mno-lvi-hardening"] = true,
            ["-mno-memops"] = true,
            ["-mno-lvi-cfi"] = true,
            ["-ffixed-x18"] = true,
            ["-fno-use-cxa-atexit"] = true,
            ["-menable-unsafe-fp-math"] = true,
            ["-print-resource-dir"] = true,
            ["-freg-struct-return"] = true,
            ["-mno-hvx-qfloat"] = true,
            ["-fno-complete-member-pointers"] = true,
            ["-fcolor-diagnostics"] = true,
            ["-fdiagnostics-print-source-range-info"] = true,
            ["-fno-autolink"] = true,
            ["-fdiagnostics-show-note-include-stack"] = true,
            ["--config"] = true,
            ["-ffixed-x28"] = true,
            ["-fno-profile-generate"] = true,
            ["-fstack-clash-protection"] = true,
            ["-fcall-saved-x13"] = true,
            ["-fasync-exceptions"] = true,
            ["-ffixed-x12"] = true,
            ["-fcommon"] = true,
            ["-dI"] = true,
            ["--hip-link"] = true,
            ["--verify-debug-info"] = true,
            ["-fno-offload-lto"] = true,
            ["-fmodules-decluse"] = true,
            ["-pthread"] = true,
            ["-fno-operator-names"] = true,
            ["-iwithprefix"] = true,
            ["-print-target-triple"] = true,
            ["-fno-cxx-modules"] = true,
            ["-fsanitize-address-use-after-scope"] = true,
            ["-fno-gpu-defer-diag"] = true,
            ["-mno-hvx"] = true,
            ["-fjump-tables"] = true,
            ["-fno-delayed-template-parsing"] = true,
            ["-fno-builtin"] = true,
            ["-serialize-diagnostics"] = true,
            ["-fno-global-isel"] = true,
            ["-fwritable-strings"] = true,
            ["-fallow-editor-placeholders"] = true,
            ["-fstack-size-section"] = true,
            ["-fsanitize-cfi-cross-dso"] = true,
            ["-fvectorize"] = true,
            ["-fmerge-all-constants"] = true,
            ["-arch"] = true,
            ["-fobjc-disable-direct-methods-for-testing"] = true,
            ["-fno-merge-all-constants"] = true,
            ["-fsjlj-exceptions"] = true,
            ["-fsplit-lto-unit"] = true,
            ["-Tbss"] = true,
            ["-flegacy-pass-manager"] = true,
            ["-mlvi-hardening"] = true,
            ["-objcmt-migrate-property"] = true,
            ["-cxx-isystem"] = true,
            ["-fno-digraphs"] = true,
            ["-dependency-file"] = true,
            ["-mno-outline-atomics"] = true,
            ["-fno-plt"] = true,
            ["-fno-sanitize-stats"] = true,
            ["-ffixed-x21"] = true,
            ["-fdiagnostics-show-option"] = true,
            ["-fcs-profile-generate"] = true,
            ["-ffixed-d5"] = true,
            ["-MP"] = true,
            ["--start-no-unused-arguments"] = true,
            ["-frtlib-add-rpath"] = true,
            ["-fno-stack-protector"] = true,
            ["-fropi"] = true,
            ["-iwithprefixbefore"] = true,
            ["-ffixed-a5"] = true,
            ["-mgeneral-regs-only"] = true,
            ["-imacros"] = true,
            ["-fno-hip-new-launch-api"] = true,
            ["-fno-temp-file"] = true,
            ["-fpch-instantiate-templates"] = true,
            ["-mtls-direct-seg-refs"] = true,
            ["-fno-sanitize-hwaddress-experimental-aliasing"] = true,
            ["-fintegrated-as"] = true,
            ["-fsycl"] = true,
            ["-rewrite-objc"] = true,
            ["-fno-preserve-as-comments"] = true,
            ["-ffixed-x24"] = true,
            ["-mrestrict-it"] = true,
            ["-fno-sanitize-trap"] = true,
            ["-fno-elide-type"] = true,
            ["-fno-hip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-Tdata"] = true,
            ["-ivfsoverlay"] = true,
            ["-fno-legacy-pass-manager"] = true,
            ["-femulated-tls"] = true,
            ["-fcuda-approx-transcendentals"] = true,
            ["-fapprox-func"] = true,
            ["-fintegrated-cc1"] = true,
            ["-mno-extern-sdata"] = true,
            ["-iquote"] = true,
            ["-fcall-saved-x9"] = true,
            ["-fgnu-keywords"] = true,
            ["-fshow-skipped-includes"] = true,
            ["--no-gpu-bundle-output"] = true,
            ["-fmodules-search-all"] = true,
            ["-fprotect-parens"] = true,
            ["-mno-long-calls"] = true,
            ["-fregister-global-dtors-with-atexit"] = true,
            ["-fno-zero-initialized-in-bss"] = true,
            ["-faapcs-bitfield-width"] = true,
            ["-fno-strict-float-cast-overflow"] = true,
            ["-fpseudo-probe-for-profiling"] = true,
            ["-fsanitize-memory-track-origins"] = true,
            ["-Xcuda-fatbinary"] = true,
            ["-fno-show-column"] = true,
            ["-fmodules-strict-decluse"] = true,
            ["-mno-code-object-v3"] = true,
            ["-ffixed-r9"] = true,
            ["-munsafe-fp-atomics"] = true,
            ["-fpcc-struct-return"] = true,
            ["-I-"] = true,
            ["-fsplit-stack"] = true,
            ["-munaligned-access"] = true,
            ["-fno-sanitize-address-use-after-scope"] = true,
            ["-mno-stack-arg-probe"] = true,
            ["-mwavefrontsize64"] = true,
            ["-objcmt-migrate-instancetype"] = true,
            ["-funique-basic-block-section-names"] = true,
            ["-fno-profile-instr-generate"] = true,
            ["-mno-nvs"] = true,
            ["-mmsa"] = true,
            ["-MG"] = true,
            ["-v"] = true,
            ["--cuda-noopt-device-debug"] = true,
            ["-fsanitize-hwaddress-experimental-aliasing"] = true,
            ["-objcmt-migrate-all"] = true,
            ["-fstack-protector-all"] = true,
            ["-mno-implicit-float"] = true,
            ["-fstack-usage"] = true,
            ["-fcall-saved-x18"] = true,
            ["-emit-llvm"] = true,
            ["-fgpu-rdc"] = true,
            ["-pg"] = true,
            ["-ffixed-x29"] = true,
            ["-fno-show-source-location"] = true,
            ["-gdwarf-2"] = true,
            ["-fapplication-extension"] = true,
            ["-fgpu-sanitize"] = true,
            ["-ffine-grained-bitfield-accesses"] = true,
            ["--cuda-path-ignore-env"] = true,
            ["-ffixed-x30"] = true,
            ["-fno-strict-return"] = true,
            ["-frelaxed-template-template-args"] = true,
            ["-frwpi"] = true,
            ["-idirafter"] = true,
            ["-mpacked-stack"] = true,
            ["-fstrict-vtable-pointers"] = true,
            ["-U"] = true,
            ["-module-dependency-dir"] = true,
            ["-fminimize-whitespace"] = true,
            ["-fhip-new-launch-api"] = true,
            ["-fobjc-arc-exceptions"] = true,
            ["-x"] = true,
            ["-gno-inline-line-tables"] = true,
            ["-fno-elide-constructors"] = true,
            ["-fsanitize-memory-use-after-dtor"] = true,
            ["-gdwarf-5"] = true,
            ["-fembed-bitcode"] = true,
            ["-mcmse"] = true,
            ["-fno-debug-macro"] = true,
            ["-cl-finite-math-only"] = true,
            ["-fmodules-validate-input-files-content"] = true,
            ["-MV"] = true,
            ["-ffreestanding"] = true,
            ["-fsanitize-cfi-canonical-jump-tables"] = true,
            ["-cl-no-signed-zeros"] = true,
            ["-w"] = true,
            ["-MF"] = true,
            ["-objcmt-allowlist-dir-path"] = true,
            ["-print-effective-triple"] = true,
            ["-fno-dollars-in-identifiers"] = true,
            ["-cl-denorms-are-zero"] = true,
            ["-mnvs"] = true,
            ["-print-search-dirs"] = true,
            ["-ffixed-a1"] = true,
            ["-fforce-dwarf-frame"] = true,
            ["-foffload-lto"] = true,
            ["-fsanitize-thread-memory-access"] = true,
            ["-print-ivar-layout"] = true,
            ["-iprefix"] = true,
            ["-isystem-after"] = true,
            ["-extract-api"] = true,
            ["-fno-stack-clash-protection"] = true,
            ["-fcuda-short-ptr"] = true,
            ["-fmodules-disable-diagnostic-validation"] = true,
            ["-mincremental-linker-compatible"] = true,
            ["-fno-xray-function-index"] = true,
            ["-fdiscard-value-names"] = true,
            ["-fno-sanitize-cfi-cross-dso"] = true,
            ["-fsanitize-address-poison-custom-array-cookie"] = true,
            ["-iframeworkwithsysroot"] = true,
            ["-objcmt-migrate-literals"] = true,
            ["-isystem"] = true,
            ["-relocatable-pch"] = true,
            ["-fno-objc-infer-related-result-type"] = true,
            ["-fdiagnostics-show-template-tree"] = true,
            ["-MT"] = true,
            ["-fobjc-exceptions"] = true,
            ["-cl-strict-aliasing"] = true,
            ["-ibuiltininc"] = true,
            ["-fstack-protector-strong"] = true,
            ["-fprofile-sample-accurate"] = true,
            ["-ffixed-x22"] = true,
            ["-mllvm"] = true,
            ["-fcxx-exceptions"] = true,
            ["-MMD"] = true,
            ["-fdirect-access-external-data"] = true,
            ["-gline-tables-only"] = true,
            ["-fno-rtti"] = true,
            ["-objcmt-ns-nonatomic-iosonly"] = true,
            ["-mno-hvx-ieee-fp"] = true,
            ["-index-header-map"] = true,
            ["-L"] = true,
            ["-fno-unique-section-names"] = true,
            ["-mqdsp6-compat"] = true,
            ["-fseh-exceptions"] = true,
            ["-ftrigraphs"] = true,
            ["-fno-profile-instr-use"] = true,
            ["-mseses"] = true,
            ["-mexecute-only"] = true,
            ["-ffixed-x2"] = true,
            ["-mlong-double-80"] = true,
            ["--migrate"] = true,
            ["-objcmt-migrate-annotation"] = true,
            ["-fwasm-exceptions"] = true,
            ["-ffixed-x4"] = true,
            ["-fno-jump-tables"] = true,
            ["-fmath-errno"] = true,
            ["-fno-constant-cfstrings"] = true,
            ["-mamdgpu-ieee"] = true,
            ["-fno-use-init-array"] = true,
            ["-time"] = true,
            ["-fdollars-in-identifiers"] = true,
            ["-ffixed-d3"] = true,
            ["-fmodules-validate-system-headers"] = true,
            ["-dM"] = true,
            ["-print-supported-cpus"] = true,
            ["-ffixed-x7"] = true,
            ["-ffixed-x10"] = true,
            ["-fcoverage-mapping"] = true,
            ["--cuda-host-only"] = true,
            ["-fgpu-flush-denormals-to-zero"] = true,
            ["-ffixed-x5"] = true,
            ["-fnew-infallible"] = true,
            ["-cl-single-precision-constant"] = true,
            ["-MJ"] = true,
            ["-mnvj"] = true,
            ["-mlong-double-128"] = true,
            ["-fxray-ignore-loops"] = true,
            ["-fopenmp"] = true,
            ["-fms-compatibility"] = true,
            ["-fcall-saved-x12"] = true,
            ["-fno-sanitize-address-use-odr-indicator"] = true,
            ["-mcrc"] = true,
            ["-finstrument-functions"] = true,
            ["-objcmt-migrate-readwrite-property"] = true,
            ["-ffixed-x16"] = true,
            ["-fpch-debuginfo"] = true,
            ["-fno-sanitize-memory-use-after-dtor"] = true,
            ["-maix-struct-return"] = true,
            ["-nobuiltininc"] = true,
            ["-ffixed-r19"] = true,
            ["-fopenmp-target-debug"] = true,
            ["-fno-sanitize-thread-memory-access"] = true,
            ["-fobjc-arc"] = true,
            ["-fdelete-null-pointer-checks"] = true,
            ["-ffixed-d6"] = true,
            ["-ffixed-x19"] = true,
            ["-fxl-pragma-pack"] = true,
            ["-fembed-bitcode-marker"] = true,
            ["-fstandalone-debug"] = true,
            ["-nogpulib"] = true,
            ["-dependency-dot"] = true,
            ["-Qn"] = true,
            ["-finstrument-function-entry-bare"] = true,
            ["-shared-libsan"] = true,
            ["-ffixed-point"] = true,
            ["-gcodeview-ghash"] = true,
            ["-msave-restore"] = true,
            ["-fdouble-square-bracket-attributes"] = true,
            ["-fvirtual-function-elimination"] = true,
            ["-fgpu-allow-device-init"] = true,
            ["-forder-file-instrumentation"] = true,
            ["-gno-embed-source"] = true,
            ["-fno-force-enable-int128"] = true,
            ["-mno-nvj"] = true,
            ["-save-stats"] = true,
            ["-fapple-pragma-pack"] = true,
            ["-z"] = true,
            ["-emit-ast"] = true,
            ["-dD"] = true,
            ["-g"] = true,
            ["-H"] = true,
            ["-ffixed-x1"] = true,
            ["-D"] = true,
            ["-fno-visibility-inlines-hidden-static-local-var"] = true,
            ["-mno-msa"] = true,
            ["-faligned-allocation"] = true,
            ["-fgnu-runtime"] = true,
            ["-ffixed-a0"] = true,
            ["-fno-exceptions"] = true,
            ["-fprofile-generate"] = true,
            ["-mno-execute-only"] = true,
            ["-fno-discard-value-names"] = true,
            ["-cl-fast-relaxed-math"] = true,
            ["-fforce-enable-int128"] = true,
            ["-fpascal-strings"] = true,
            ["-mno-seses"] = true,
            ["-mnocrc"] = true,
            ["-module-file-info"] = true,
            ["-fshort-enums"] = true,
            ["-fdelayed-template-parsing"] = true,
            ["-mmemops"] = true,
            ["-mlong-double-64"] = true,
            ["-ffixed-d1"] = true,
            ["-fcall-saved-x8"] = true,
            ["-o"] = true,
            ["-include"] = true,
            ["-gdwarf64"] = true,
            ["-ffixed-x20"] = true,
            ["-Xanalyzer"] = true,
            ["-Ttext"] = true,
            ["--no-cuda-version-check"] = true,
            ["-fsystem-module"] = true,
            ["-fverbose-asm"] = true,
            ["-C"] = true,
            ["-mlocal-sdata"] = true,
            ["-gmodules"] = true,
            ["-ffixed-a2"] = true,
            ["-emit-module"] = true,
            ["-mgpopt"] = true,
            ["-mskip-rax-setup"] = true,
            ["-finline-functions"] = true,
            ["-fzvector"] = true,
            ["-cl-kernel-arg-info"] = true,
            ["-fno-memory-profile"] = true,
            ["-mabicalls"] = true,
            ["-fno-color-diagnostics"] = true,
            ["-ffixed-x23"] = true,
            ["-freroll-loops"] = true,
            ["-fno-spell-checking"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-emit-merged-ifs"] = true,
            ["-fno-aapcs-bitfield-width"] = true,
            ["-mstackrealign"] = true,
            ["-fmodules-validate-once-per-build-session"] = true,
            ["-cl-mad-enable"] = true,
            ["-traditional-cpp"] = true,
            ["-ffixed-x6"] = true,
            ["-mlvi-cfi"] = true,
            ["-fno-coverage-mapping"] = true,
            ["-fno-signed-char"] = true,
            ["-ffunction-sections"] = true,
            ["-fsanitize-address-outline-instrumentation"] = true,
            ["-G"] = true,
            ["--version"] = true,
            ["-B"] = true,
            ["-ffast-math"] = true,
            ["-ffixed-d2"] = true,
            ["-fno-threadsafe-statics"] = true,
            ["-fsigned-char"] = true,
            ["-fsave-optimization-record"] = true,
            ["-fmodules"] = true,
            ["-mno-neg-immediates"] = true,
            ["-mmadd4"] = true,
            ["-freciprocal-math"] = true,
            ["-objcmt-migrate-ns-macros"] = true,
            ["-c"] = true,
            ["-fno-gnu-inline-asm"] = true,
            ["-fpch-validate-input-files-content"] = true,
            ["-mno-wavefrontsize64"] = true,
            ["-mfp64"] = true,
            ["-mno-local-sdata"] = true,
            ["-fno-fine-grained-bitfield-accesses"] = true,
            ["-fansi-escape-codes"] = true,
            ["-fvisibility-inlines-hidden-static-local-var"] = true,
            ["-fno-assume-sane-operator-new"] = true,
            ["-ffixed-x3"] = true,
            ["-finstrument-functions-after-inlining"] = true,
            ["-pipe"] = true,
            ["-I"] = true,
            ["-moutline"] = true,
            ["-ffinite-loops"] = true,
            ["-fbuiltin-module-map"] = true,
            ["-ffixed-x27"] = true,
            ["-mibt-seal"] = true,
            ["-fno-common"] = true,
            ["-cl-opt-disable"] = true,
            ["--precompile"] = true,
            ["-iwithsysroot"] = true,
            ["-mfix-cmse-cve-2021-35465"] = true,
            ["-CC"] = true,
            ["-fimplicit-module-maps"] = true,
            ["-fno-crash-diagnostics"] = true,
            ["--help-hidden"] = true,
            ["-fsanitize-cfi-icall-generalize-pointers"] = true,
            ["-mno-restrict-it"] = true,
            ["-fms-extensions"] = true,
            ["-fhip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fwhole-program-vtables"] = true,
            ["-b"] = true,
            ["-fuse-line-directives"] = true,
            ["-fstrict-enums"] = true,
            ["-fno-delete-null-pointer-checks"] = true,
            ["-dsym-dir"] = true,
            ["-mno-packets"] = true,
            ["-ftrapv"] = true,
            ["-fno-pch-debuginfo"] = true,
            ["-fcomplete-member-pointers"] = true,
            ["-fsanitize-trap"] = true,
            ["-finline-hint-functions"] = true,
            ["-static-openmp"] = true,
            ["-mmt"] = true,
            ["-working-directory"] = true,
            ["-fno-cuda-approx-transcendentals"] = true,
            ["-fdigraphs"] = true,
            ["-E"] = true,
            ["-gdwarf32"] = true,
            ["-fopenmp-extensions"] = true,
            ["-fcall-saved-x15"] = true,
            ["-moutline-atomics"] = true,
            ["-fno-eliminate-unused-debug-types"] = true,
            ["-ffixed-x31"] = true,
            ["-mno-incremental-linker-compatible"] = true,
            ["-fno-unroll-loops"] = true,
            ["--cuda-compile-host-device"] = true,
            ["-fno-sanitize-thread-func-entry-exit"] = true,
            ["-fexperimental-strict-floating-point"] = true,
            ["-miamcu"] = true,
            ["-membedded-data"] = true,
            ["-pedantic"] = true,
            ["-fno-sanitize-address-outline-instrumentation"] = true,
            ["-Xpreprocessor"] = true,
            ["-fno-finite-loops"] = true,
            ["-gembed-source"] = true,
            ["-fdeclspec"] = true,
            ["-objcmt-returns-innerpointer-property"] = true,
            ["-fignore-exceptions"] = true,
            ["-fconvergent-functions"] = true,
            ["-ffixed-x14"] = true,
            ["-fdebug-types-section"] = true,
            ["-fno-declspec"] = true,
            ["-ffixed-x15"] = true,
            ["-fvalidate-ast-input-files-content"] = true,
            ["-fsanitize-memory-param-retval"] = true,
            ["-gdwarf-4"] = true,
            ["-MM"] = true,
            ["-fborland-extensions"] = true,
            ["-fno-integrated-as"] = true,
            ["-fno-direct-access-external-data"] = true,
            ["-cl-no-stdinc"] = true,
            ["-objcmt-migrate-readonly-property"] = true,
            ["-fcall-saved-x11"] = true,
            ["-include-pch"] = true,
            ["-fno-diagnostics-fixit-info"] = true,
            ["-fno-register-global-dtors-with-atexit"] = true,
            ["-fsanitize-address-use-odr-indicator"] = true,
            ["-ffixed-a6"] = true,
            ["-Xlinker"] = true,
            ["-fmodules-ts"] = true,
            ["-ffixed-x8"] = true,
            ["-fsized-deallocation"] = true,
            ["-objcmt-migrate-designated-init"] = true,
            ["-mpackets"] = true,
            ["-print-multiarch"] = true,
            ["-print-rocm-search-dirs"] = true,
            ["-msoft-float"] = true,
            ["-fcall-saved-x14"] = true,
            ["-fno-split-machine-functions"] = true,
            ["-femit-all-decls"] = true,
            ["-faddrsig"] = true,
            ["-ffixed-d7"] = true,
            ["--end-no-unused-arguments"] = true,
            ["-mno-unaligned-access"] = true,
            ["-mrtd"] = true,
            ["-Wdeprecated"] = true,
            ["-rpath"] = true,
            ["-mno-save-restore"] = true,
            ["-Xclang"] = true,
            ["-mstack-arg-probe"] = true,
            ["-ffixed-x9"] = true,
            ["-fobjc-weak"] = true,
            ["-fsanitize-thread-atomics"] = true,
            ["-fdebug-ranges-base-address"] = true,
            ["-mthread-model"] = true,
            ["-fslp-vectorize"] = true,
            ["-flto"] = true,
            ["-mfix-cortex-a53-835769"] = true,
            ["-mno-gpopt"] = true,
            ["-ffixed-x17"] = true,
            ["-fno-sanitize-cfi-canonical-jump-tables"] = true,
            ["-fno-openmp-extensions"] = true,
            ["--analyze"] = true,
            ["-ffixed-x11"] = true,
            ["-mno-relax"] = true,
            ["-fxray-always-emit-customevents"] = true,
            ["-fno-short-wchar"] = true,
            ["-fno-sanitize-memory-param-retval"] = true,
            ["-Xcuda-ptxas"] = true,
            ["-cl-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-mhvx-ieee-fp"] = true,
            ["-ffixed-a3"] = true,
            ["-fstack-protector"] = true,
            ["-ffixed-d0"] = true,
            ["-fexceptions"] = true,
            ["-mcumode"] = true,
            ["-fno-addrsig"] = true,
            ["-cl-unsafe-math-optimizations"] = true,
            ["-Qunused-arguments"] = true,
            ["-fno-fixed-point"] = true,
            ["-fno-sanitize-thread-atomics"] = true,
            ["-F"] = true,
            ["-mno-mt"] = true,
            ["-Qy"] = true,
            ["-fno-sanitize-memory-track-origins"] = true,
            ["-mno-tgsplit"] = true,
            ["-fsplit-machine-functions"] = true,
            ["-funique-internal-linkage-names"] = true,
            ["-msvr4-struct-return"] = true
        }
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolld"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["lib.detect.has_flags"] = {
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_sh__-shared -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-std=c++20"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-O3"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-DNDEBUG"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-Oz"] = true
    },
    find_program = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    }
}