{
    ["ndk_arch_arm64-v8a_plat_android"] = {
        cross = "aarch64-linux-android-",
        plat = "android",
        arch = "arm64-v8a",
        ndk_sdkver = "21",
        __global = true,
        ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
        ndkver = 25,
        llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
        __checked = true,
        bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
        ndk_sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]]
    },
    msvc_arch_x64_plat_windows = {
        plat = "windows",
        __checked = false,
        arch = "x64"
    },
    go_arch_x86_64_plat_msys = {
        plat = "msys",
        __checked = true,
        arch = "x86_64"
    },
    ["envs_arch_arm64-v8a_plat_android"] = {
        plat = "android",
        __checked = true,
        arch = "arm64-v8a",
        __global = true
    },
    ["tool_target_preloader_android_arm64-v8a_cxx"] = {
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolchain_info = {
            plat = "android",
            arch = "arm64-v8a",
            name = "ndk",
            cachekey = "ndk_arch_arm64-v8a_plat_android"
        },
        toolname = "clangxx"
    },
    envs_arch_x86_64_plat_msys = {
        plat = "msys",
        __checked = true,
        arch = "x86_64"
    },
    yasm_arch_x86_64_plat_msys = {
        plat = "msys",
        __checked = true,
        arch = "x86_64"
    },
    ["rust_arch_arm64-v8a_plat_android"] = {
        plat = "android",
        __checked = true,
        arch = "arm64-v8a",
        __global = true
    },
    gcc_arch_x86_64_plat_msys = {
        plat = "msys",
        __checked = false,
        arch = "x86_64"
    },
    gfortran_arch_x86_64_plat_msys = {
        plat = "msys",
        __checked = true,
        arch = "x86_64"
    },
    clang_arch_x86_64_plat_msys = {
        plat = "msys",
        __checked = false,
        arch = "x86_64"
    },
    ["tool_target_preloader_android_arm64-v8a_sh"] = {
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolchain_info = {
            plat = "android",
            arch = "arm64-v8a",
            name = "ndk",
            cachekey = "ndk_arch_arm64-v8a_plat_android"
        },
        toolname = "clangxx"
    }
}