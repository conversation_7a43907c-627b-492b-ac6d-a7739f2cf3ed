{
    ["rust_arch_armeabi-v7a_plat_android"] = {
        plat = "android",
        arch = "armeabi-v7a",
        __checked = true,
        __global = true
    },
    yasm_arch_x86_64_plat_msys = {
        plat = "msys",
        arch = "x86_64",
        __checked = true
    },
    go_arch_x86_64_plat_msys = {
        plat = "msys",
        arch = "x86_64",
        __checked = true
    },
    ["tool_target_preloader_android_armeabi-v7a_cxx"] = {
        toolname = "clangxx",
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolchain_info = {
            plat = "android",
            arch = "armeabi-v7a",
            name = "ndk",
            cachekey = "ndk_arch_armeabi-v7a_plat_android"
        }
    },
    msvc_arch_x64_plat_windows = {
        plat = "windows",
        arch = "x64",
        __checked = false
    },
    clang_arch_x86_64_plat_msys = {
        plat = "msys",
        arch = "x86_64",
        __checked = false
    },
    ["ndk_arch_armeabi-v7a_plat_android"] = {
        plat = "android",
        arch = "armeabi-v7a",
        cross = "arm-linux-androideabi-",
        llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
        __global = true,
        ndkver = 25,
        bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
        __checked = true,
        ndk_sdkver = "21",
        ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
        ndk_sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]]
    },
    ["envs_arch_armeabi-v7a_plat_android"] = {
        plat = "android",
        arch = "armeabi-v7a",
        __checked = true,
        __global = true
    },
    gcc_arch_x86_64_plat_msys = {
        plat = "msys",
        arch = "x86_64",
        __checked = false
    },
    envs_arch_x86_64_plat_msys = {
        plat = "msys",
        arch = "x86_64",
        __checked = true
    },
    gfortran_arch_x86_64_plat_msys = {
        plat = "msys",
        arch = "x86_64",
        __checked = true
    }
}