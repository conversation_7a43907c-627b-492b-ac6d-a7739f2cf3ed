{
    find_program_fetch_package_xmake = {
        ninja = false,
        cmake = false
    },
    ["find_program_ndk_arch_arm64-v8a_plat_android_checktoolcxx"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    find_programver = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = "14.0.7"
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    },
    ["find_program_ndk_arch_arm64-v8a_plat_android_checktoolsh"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
            cross = "aarch64-linux-android-",
            sdkver = "21",
            ndkver = 25,
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]]
        }
    },
    ["find_program_ndk_arch_arm64-v8a_plat_android_checktoolld"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    find_program = {
        git = [[C:\Program Files\Git\mingw64\bin\git.exe]],
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        gcc = false,
        gzip = [[C:\Program Files\Git\usr\bin\gzip.exe]],
        ["vswhere.exe"] = [[C:\Program Files (x86)\Microsoft Visual Studio\Installer\vswhere.exe]],
        tar = [[C:\Program Files\Git\usr\bin\tar.exe]],
        clang = false
    },
    ["lib.detect.has_flags"] = {
        ["android_arm64-v8a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\aarch64-linux-android -nostdinc++ -Qunused-arguments --target=aarch64-none-linux-android21_-Oz"] = true,
        ["android_arm64-v8a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\aarch64-linux-android -nostdinc++ -Qunused-arguments --target=aarch64-none-linux-android21_-DNDEBUG"] = true,
        ["android_arm64-v8a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\aarch64-linux-android -nostdinc++ -Qunused-arguments --target=aarch64-none-linux-android21_-fvisibility-inlines-hidden"] = true,
        ["android_arm64-v8a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\aarch64-linux-android -nostdinc++ -Qunused-arguments --target=aarch64-none-linux-android21_-std=c++20"] = true,
        ["android_arm64-v8a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\aarch64-linux-android -nostdinc++ -Qunused-arguments --target=aarch64-none-linux-android21_-fPIC"] = true
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["--Bstatic"] = true,
            ["--exclude-all-symbols"] = true,
            ["--disable-auto-import"] = true,
            ["--fatal-warnings"] = true,
            ["-L"] = true,
            ["--allow-multiple-definition"] = true,
            ["-l"] = true,
            ["--disable-high-entropy-va"] = true,
            ["-static"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["--disable-dynamicbase"] = true,
            ["--disable-no-seh"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["--no-demangle"] = true,
            ["-dy"] = true,
            ["--insert-timestamp"] = true,
            ["--shared"] = true,
            ["--appcontainer"] = true,
            ["--demangle"] = true,
            ["-v"] = true,
            ["--verbose"] = true,
            ["--disable-nxcompat"] = true,
            ["--help"] = true,
            ["--export-all-symbols"] = true,
            ["--kill-at"] = true,
            ["--disable-tsaware"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["--dynamicbase"] = true,
            ["--gc-sections"] = true,
            ["--large-address-aware"] = true,
            ["--strip-all"] = true,
            ["--high-entropy-va"] = true,
            ["--no-dynamicbase"] = true,
            ["--whole-archive"] = true,
            ["--no-whole-archive"] = true,
            ["--no-allow-multiple-definition"] = true,
            ["--no-fatal-warnings"] = true,
            ["-m"] = true,
            ["--strip-debug"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["--no-insert-timestamp"] = true,
            ["--Bdynamic"] = true,
            ["--enable-auto-import"] = true,
            ["--version"] = true,
            ["-o"] = true,
            ["--nxcompat"] = true,
            ["--no-seh"] = true,
            ["--tsaware"] = true,
            ["-S"] = true,
            ["--no-gc-sections"] = true,
            ["-dn"] = true,
            ["-s"] = true
        }
    },
    ["find_package_android_arm64-v8a_fetch_package_xmake"] = {
        ["xmake::fmt_f749017f3ca64e85a0f49e295f99ba5d_release_10.2.1_external"] = {
            libfiles = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\f749017f3ca64e85a0f49e295f99ba5d\lib\libfmt.a]]
            },
            static = true,
            version = "10.2.1",
            links = {
                "fmt"
            },
            linkdirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\f749017f3ca64e85a0f49e295f99ba5d\lib]]
            },
            license = "MIT",
            sysincludedirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\f749017f3ca64e85a0f49e295f99ba5d\include]]
            }
        },
        ["xmake::nlohmann_json_0fcf5437be6340d38dc2a43e4809f2f5_release_v3.11.3_external"] = {
            version = "v3.11.3",
            license = "MIT",
            sysincludedirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\n\nlohmann_json\v3.11.3\0fcf5437be6340d38dc2a43e4809f2f5\include]]
            }
        }
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-fpcc-struct-return"] = true,
            ["-mno-lvi-hardening"] = true,
            ["-fno-rtti"] = true,
            ["-fcuda-approx-transcendentals"] = true,
            ["--cuda-compile-host-device"] = true,
            ["-fcall-saved-x9"] = true,
            ["-fcolor-diagnostics"] = true,
            ["-fobjc-arc-exceptions"] = true,
            ["-ffixed-a1"] = true,
            ["-gline-tables-only"] = true,
            ["-mno-outline"] = true,
            ["--help-hidden"] = true,
            ["-ffixed-x1"] = true,
            ["-ffixed-x15"] = true,
            ["-fgpu-allow-device-init"] = true,
            ["-ffixed-x12"] = true,
            ["-ffixed-x23"] = true,
            ["-fno-gpu-defer-diag"] = true,
            ["-mcrc"] = true,
            ["-ffixed-x7"] = true,
            ["-Xanalyzer"] = true,
            ["-fopenmp-target-new-runtime"] = true,
            ["-ffixed-x3"] = true,
            ["-femit-all-decls"] = true,
            ["-finstrument-functions"] = true,
            ["-mbranches-within-32B-boundaries"] = true,
            ["-fmodules-search-all"] = true,
            ["-fno-access-control"] = true,
            ["-mno-bti-at-return-twice"] = true,
            ["-iframeworkwithsysroot"] = true,
            ["-mno-restrict-it"] = true,
            ["-mno-madd4"] = true,
            ["--emit-static-lib"] = true,
            ["-fno-signed-char"] = true,
            ["-ffixed-a2"] = true,
            ["-fxray-instrument"] = true,
            ["-rewrite-legacy-objc"] = true,
            ["-rewrite-objc"] = true,
            ["-ffixed-x13"] = true,
            ["-ffixed-x27"] = true,
            ["-mcode-object-v3"] = true,
            ["--no-gpu-bundle-output"] = true,
            ["-dependency-dot"] = true,
            ["-fms-hotpatch"] = true,
            ["-fverbose-asm"] = true,
            ["-fno-split-stack"] = true,
            ["-ivfsoverlay"] = true,
            ["-cl-no-signed-zeros"] = true,
            ["-fsanitize-address-use-odr-indicator"] = true,
            ["-fno-hip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-iwithsysroot"] = true,
            ["-fpch-instantiate-templates"] = true,
            ["-mextern-sdata"] = true,
            ["-mlong-double-64"] = true,
            ["-working-directory"] = true,
            ["-fno-keep-static-consts"] = true,
            ["-mno-fix-cmse-cve-2021-35465"] = true,
            ["-fmodules-user-build-path"] = true,
            ["-fdebug-info-for-profiling"] = true,
            ["-mno-code-object-v3"] = true,
            ["-moutline-atomics"] = true,
            ["-dM"] = true,
            ["-fno-pch-debuginfo"] = true,
            ["-fsanitize-thread-memory-access"] = true,
            ["-include-pch"] = true,
            ["-fno-debug-macro"] = true,
            ["-ffixed-x18"] = true,
            ["-mexecute-only"] = true,
            ["-mskip-rax-setup"] = true,
            ["-malign-double"] = true,
            ["-B"] = true,
            ["-gline-directives-only"] = true,
            ["-arcmt-migrate-report-output"] = true,
            ["-mno-hvx-ieee-fp"] = true,
            ["-mmadd4"] = true,
            ["-mmark-bti-property"] = true,
            ["-fcoroutines-ts"] = true,
            ["-mno-cumode"] = true,
            ["-fobjc-arc"] = true,
            ["-fansi-escape-codes"] = true,
            ["-fopenmp"] = true,
            ["-index-header-map"] = true,
            ["-mno-local-sdata"] = true,
            ["-fno-use-init-array"] = true,
            ["-c"] = true,
            ["-print-resource-dir"] = true,
            ["-iwithprefix"] = true,
            ["-fmerge-all-constants"] = true,
            ["-gcodeview-ghash"] = true,
            ["-C"] = true,
            ["-mno-mt"] = true,
            ["-fno-openmp-extensions"] = true,
            ["-fno-operator-names"] = true,
            ["-time"] = true,
            ["-fstandalone-debug"] = true,
            ["-fno-sanitize-hwaddress-experimental-aliasing"] = true,
            ["-fno-strict-float-cast-overflow"] = true,
            ["-mno-save-restore"] = true,
            ["--no-cuda-version-check"] = true,
            ["-D"] = true,
            ["-fno-new-infallible"] = true,
            ["-fglobal-isel"] = true,
            ["-fno-exceptions"] = true,
            ["-fgnu-runtime"] = true,
            ["-fstrict-float-cast-overflow"] = true,
            ["-fallow-editor-placeholders"] = true,
            ["-fmodules-strict-decluse"] = true,
            ["-fdebug-ranges-base-address"] = true,
            ["-nobuiltininc"] = true,
            ["-fno-autolink"] = true,
            ["-fgnu89-inline"] = true,
            ["-ffixed-x17"] = true,
            ["-mwavefrontsize64"] = true,
            ["-ffixed-d0"] = true,
            ["-MF"] = true,
            ["-fno-discard-value-names"] = true,
            ["-fshort-enums"] = true,
            ["-E"] = true,
            ["-fshow-skipped-includes"] = true,
            ["-imacros"] = true,
            ["-fno-unroll-loops"] = true,
            ["-fmodules-ts"] = true,
            ["-mno-ms-bitfields"] = true,
            ["-msvr4-struct-return"] = true,
            ["--analyzer-output"] = true,
            ["-ffixed-point"] = true,
            ["-fstrict-vtable-pointers"] = true,
            ["-fsanitize-thread-atomics"] = true,
            ["-ffunction-sections"] = true,
            ["-freciprocal-math"] = true,
            ["-iquote"] = true,
            ["-fno-rtti-data"] = true,
            ["-print-rocm-search-dirs"] = true,
            ["-fno-digraphs"] = true,
            ["-ffixed-a5"] = true,
            ["-mgeneral-regs-only"] = true,
            ["-dsym-dir"] = true,
            ["-fpascal-strings"] = true,
            ["-mno-hvx-qfloat"] = true,
            ["-ffixed-x10"] = true,
            ["-fvectorize"] = true,
            ["-mno-memops"] = true,
            ["-fno-temp-file"] = true,
            ["-mnvs"] = true,
            ["-fapplication-extension"] = true,
            ["-cl-fast-relaxed-math"] = true,
            ["-cl-opt-disable"] = true,
            ["-fregister-global-dtors-with-atexit"] = true,
            ["-gno-inline-line-tables"] = true,
            ["-objcmt-migrate-readwrite-property"] = true,
            ["-MG"] = true,
            ["-mno-gpopt"] = true,
            ["-faapcs-bitfield-load"] = true,
            ["-fdiagnostics-show-note-include-stack"] = true,
            ["-mfp32"] = true,
            ["-mthread-model"] = true,
            ["-fno-split-machine-functions"] = true,
            ["-objcmt-migrate-property-dot-syntax"] = true,
            ["-fgpu-flush-denormals-to-zero"] = true,
            ["-emit-ast"] = true,
            ["-fconvergent-functions"] = true,
            ["-mstackrealign"] = true,
            ["-mmemops"] = true,
            ["-fno-assume-sane-operator-new"] = true,
            ["-fno-hip-new-launch-api"] = true,
            ["-pg"] = true,
            ["-Wdeprecated"] = true,
            ["-mhvx-qfloat"] = true,
            ["-ffixed-d1"] = true,
            ["-fapple-pragma-pack"] = true,
            ["-arch"] = true,
            ["-cl-denorms-are-zero"] = true,
            ["-fdebug-types-section"] = true,
            ["-fno-use-cxa-atexit"] = true,
            ["-Qn"] = true,
            ["-fstack-protector-strong"] = true,
            ["-fno-rtlib-add-rpath"] = true,
            ["-fno-strict-return"] = true,
            ["-cxx-isystem"] = true,
            ["-mno-fix-cortex-a53-835769"] = true,
            ["-mno-abicalls"] = true,
            ["-ffast-math"] = true,
            ["-mlong-calls"] = true,
            ["--migrate"] = true,
            ["-funroll-loops"] = true,
            ["-fcall-saved-x18"] = true,
            ["-emit-llvm"] = true,
            ["-ffixed-x22"] = true,
            ["-mnop-mcount"] = true,
            ["-fropi"] = true,
            ["-mhvx-ieee-fp"] = true,
            ["-fdiagnostics-show-hotness"] = true,
            ["-ffixed-d2"] = true,
            ["-fcuda-short-ptr"] = true,
            ["-frtlib-add-rpath"] = true,
            ["-mrelax"] = true,
            ["-dI"] = true,
            ["--cuda-device-only"] = true,
            ["-fdigraphs"] = true,
            ["-objcmt-migrate-designated-init"] = true,
            ["-fprotect-parens"] = true,
            ["-mno-execute-only"] = true,
            ["-I-"] = true,
            ["-fno-objc-infer-related-result-type"] = true,
            ["-fsanitize-thread-func-entry-exit"] = true,
            ["-fsanitize-address-outline-instrumentation"] = true,
            ["-fsanitize-memory-param-retval"] = true,
            ["-ffixed-r19"] = true,
            ["-fsigned-char"] = true,
            ["-fvalidate-ast-input-files-content"] = true,
            ["-objcmt-migrate-readonly-property"] = true,
            ["-fno-sycl"] = true,
            ["-cl-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fcf-protection"] = true,
            ["-fno-coverage-mapping"] = true,
            ["-fdiagnostics-show-option"] = true,
            ["-fno-jump-tables"] = true,
            ["-fno-register-global-dtors-with-atexit"] = true,
            ["-iprefix"] = true,
            ["-fsanitize-address-use-after-scope"] = true,
            ["-cl-finite-math-only"] = true,
            ["-msave-restore"] = true,
            ["-pipe"] = true,
            ["-fstack-size-section"] = true,
            ["-mhvx"] = true,
            ["-fasync-exceptions"] = true,
            ["-fno-preserve-as-comments"] = true,
            ["-gdwarf64"] = true,
            ["-fno-sanitize-memory-use-after-dtor"] = true,
            ["-fforce-dwarf-frame"] = true,
            ["-mseses"] = true,
            ["-fdiagnostics-absolute-paths"] = true,
            ["-fxray-always-emit-customevents"] = true,
            ["-finstrument-function-entry-bare"] = true,
            ["--cuda-path-ignore-env"] = true,
            ["-objcmt-migrate-instancetype"] = true,
            ["-fno-declspec"] = true,
            ["-L"] = true,
            ["-gdwarf32"] = true,
            ["-fdebug-macro"] = true,
            ["-mrecord-mcount"] = true,
            ["-momit-leaf-frame-pointer"] = true,
            ["-fno-sanitize-thread-func-entry-exit"] = true,
            ["-fopenmp-simd"] = true,
            ["-fsystem-module"] = true,
            ["-miamcu"] = true,
            ["-pedantic"] = true,
            ["-print-ivar-layout"] = true,
            ["-MV"] = true,
            ["-fvirtual-function-elimination"] = true,
            ["-mno-crc"] = true,
            ["-munaligned-access"] = true,
            ["-gdwarf-3"] = true,
            ["-M"] = true,
            ["-module-dependency-dir"] = true,
            ["-cl-unsafe-math-optimizations"] = true,
            ["-nogpuinc"] = true,
            ["-fforce-emit-vtables"] = true,
            ["-fborland-extensions"] = true,
            ["-frelaxed-template-template-args"] = true,
            ["-msoft-float"] = true,
            ["-rpath"] = true,
            ["-fno-sanitize-address-poison-custom-array-cookie"] = true,
            ["-fapple-kext"] = true,
            ["-fmath-errno"] = true,
            ["-fdouble-square-bracket-attributes"] = true,
            ["-fcxx-exceptions"] = true,
            ["-save-stats"] = true,
            ["-dependency-file"] = true,
            ["-F"] = true,
            ["-mibt-seal"] = true,
            ["-fno-complete-member-pointers"] = true,
            ["-fno-unique-section-names"] = true,
            ["-fmodules-validate-input-files-content"] = true,
            ["-mno-wavefrontsize64"] = true,
            ["-fno-visibility-inlines-hidden-static-local-var"] = true,
            ["-MJ"] = true,
            ["-fno-constant-cfstrings"] = true,
            ["-Tdata"] = true,
            ["-mno-tgsplit"] = true,
            ["-fstrict-enums"] = true,
            ["-fembed-bitcode-marker"] = true,
            ["-fwhole-program-vtables"] = true,
            ["-mpackets"] = true,
            ["-g"] = true,
            ["-fcomplete-member-pointers"] = true,
            ["-fstack-protector-all"] = true,
            ["-Xlinker"] = true,
            ["-objcmt-migrate-annotation"] = true,
            ["-fpch-debuginfo"] = true,
            ["-fno-cxx-modules"] = true,
            ["-fms-extensions"] = true,
            ["-cl-no-stdinc"] = true,
            ["-menable-experimental-extensions"] = true,
            ["-traditional-cpp"] = true,
            ["-fno-sanitize-stats"] = true,
            ["--analyze"] = true,
            ["-fdeclspec"] = true,
            ["-Xpreprocessor"] = true,
            ["-ffixed-a6"] = true,
            ["-fno-short-wchar"] = true,
            ["-fcall-saved-x13"] = true,
            ["-mrestrict-it"] = true,
            ["-objcmt-migrate-literals"] = true,
            ["-maix-struct-return"] = true,
            ["-flegacy-pass-manager"] = true,
            ["-fdirect-access-external-data"] = true,
            ["-emit-module"] = true,
            ["-fno-merge-all-constants"] = true,
            ["-fno-double-square-bracket-attributes"] = true,
            ["-Ttext"] = true,
            ["-ffixed-x24"] = true,
            ["-fms-compatibility"] = true,
            ["-ffixed-x8"] = true,
            ["-objcmt-atomic-property"] = true,
            ["-ffixed-x21"] = true,
            ["-fgnu-keywords"] = true,
            ["-mno-packets"] = true,
            ["-mgpopt"] = true,
            ["-emit-merged-ifs"] = true,
            ["-relocatable-pch"] = true,
            ["-faligned-allocation"] = true,
            ["-mno-incremental-linker-compatible"] = true,
            ["-mmsa"] = true,
            ["--precompile"] = true,
            ["-fno-sanitize-thread-memory-access"] = true,
            ["-fno-dollars-in-identifiers"] = true,
            ["-finline-hint-functions"] = true,
            ["-mglobal-merge"] = true,
            ["-fcs-profile-generate"] = true,
            ["-fhip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fdwarf-exceptions"] = true,
            ["-objcmt-migrate-ns-macros"] = true,
            ["-fno-sanitize-address-use-after-scope"] = true,
            ["-faapcs-bitfield-width"] = true,
            ["-nostdinc"] = true,
            ["-fno-memory-profile"] = true,
            ["-fnew-infallible"] = true,
            ["-fno-force-enable-int128"] = true,
            ["-S"] = true,
            ["-fpch-validate-input-files-content"] = true,
            ["-fsjlj-exceptions"] = true,
            ["-moutline"] = true,
            ["-MQ"] = true,
            ["-fno-cuda-approx-transcendentals"] = true,
            ["-ftime-trace"] = true,
            ["-fapprox-func"] = true,
            ["-fapple-link-rtlib"] = true,
            ["-fno-common"] = true,
            ["-finline-functions"] = true,
            ["-fno-profile-instr-generate"] = true,
            ["-fxray-always-emit-typedevents"] = true,
            ["-MM"] = true,
            ["-fsanitize-trap"] = true,
            ["-fno-delete-null-pointer-checks"] = true,
            ["-fvisibility-ms-compat"] = true,
            ["-fno-sanitize-cfi-cross-dso"] = true,
            ["-objcmt-migrate-subscripting"] = true,
            ["-ffixed-x4"] = true,
            ["-idirafter"] = true,
            ["-fno-elide-constructors"] = true,
            ["-fxl-pragma-pack"] = true,
            ["-fcall-saved-x8"] = true,
            ["-fno-plt"] = true,
            ["-menable-unsafe-fp-math"] = true,
            ["-G"] = true,
            ["-mno-seses"] = true,
            ["-fno-trigraphs"] = true,
            ["-fno-sanitize-address-use-odr-indicator"] = true,
            ["-fprofile-sample-accurate"] = true,
            ["-fno-stack-protector"] = true,
            ["-mnocrc"] = true,
            ["-mamdgpu-ieee"] = true,
            ["-meabi"] = true,
            ["-fobjc-weak"] = true,
            ["-fdelete-null-pointer-checks"] = true,
            ["-mfix-cortex-a53-835769"] = true,
            ["-gdwarf"] = true,
            ["-membedded-data"] = true,
            ["-fno-sanitize-thread-atomics"] = true,
            ["-fsave-optimization-record"] = true,
            ["-shared-libsan"] = true,
            ["-mfix-cmse-cve-2021-35465"] = true,
            ["-fexperimental-strict-floating-point"] = true,
            ["-fno-stack-clash-protection"] = true,
            ["-fenable-matrix"] = true,
            ["-U"] = true,
            ["-b"] = true,
            ["-I"] = true,
            ["-fwasm-exceptions"] = true,
            ["-fcxx-modules"] = true,
            ["-mllvm"] = true,
            ["-mtgsplit"] = true,
            ["-mno-global-merge"] = true,
            ["-fvisibility-global-new-delete-hidden"] = true,
            ["-module-file-info"] = true,
            ["-cl-single-precision-constant"] = true,
            ["-print-target-triple"] = true,
            ["-MT"] = true,
            ["-ffixed-x28"] = true,
            ["-mno-stack-arg-probe"] = true,
            ["-fzvector"] = true,
            ["-fhip-new-launch-api"] = true,
            ["-mrelax-all"] = true,
            ["-fdiscard-value-names"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-MP"] = true,
            ["-H"] = true,
            ["-ibuiltininc"] = true,
            ["-fobjc-exceptions"] = true,
            ["-fno-sanitize-cfi-canonical-jump-tables"] = true,
            ["-funique-internal-linkage-names"] = true,
            ["-fsized-deallocation"] = true,
            ["-arcmt-migrate-emit-errors"] = true,
            ["-ffixed-d3"] = true,
            ["-mpacked-stack"] = true,
            ["-ffixed-d5"] = true,
            ["--end-no-unused-arguments"] = true,
            ["-objcmt-migrate-property"] = true,
            ["-mlocal-sdata"] = true,
            ["-fshort-wchar"] = true,
            ["-fslp-vectorize"] = true,
            ["-print-effective-triple"] = true,
            ["-gdwarf-2"] = true,
            ["-mno-neg-immediates"] = true,
            ["-ffixed-a3"] = true,
            ["-ffixed-a0"] = true,
            ["-cl-strict-aliasing"] = true,
            ["-dD"] = true,
            ["-fsplit-dwarf-inlining"] = true,
            ["-fsplit-stack"] = true,
            ["-mms-bitfields"] = true,
            ["--start-no-unused-arguments"] = true,
            ["-ffixed-d6"] = true,
            ["-ffixed-x30"] = true,
            ["-mcumode"] = true,
            ["-mno-embedded-data"] = true,
            ["-fminimize-whitespace"] = true,
            ["-fsanitize-stats"] = true,
            ["-fsanitize-memory-use-after-dtor"] = true,
            ["-fno-sanitize-address-outline-instrumentation"] = true,
            ["-fsanitize-hwaddress-experimental-aliasing"] = true,
            ["-fsanitize-address-poison-custom-array-cookie"] = true,
            ["-fno-delayed-template-parsing"] = true,
            ["-fblocks"] = true,
            ["-fvisibility-inlines-hidden"] = true,
            ["-femulated-tls"] = true,
            ["-fmodules"] = true,
            ["-fcall-saved-x14"] = true,
            ["-iwithprefixbefore"] = true,
            ["-fwritable-strings"] = true,
            ["-fno-fine-grained-bitfield-accesses"] = true,
            ["-ffixed-a4"] = true,
            ["-mno-lvi-cfi"] = true,
            ["-gdwarf-5"] = true,
            ["-ffreestanding"] = true,
            ["-ffixed-x11"] = true,
            ["-fprofile-instr-generate"] = true,
            ["-freroll-loops"] = true,
            ["-mno-long-calls"] = true,
            ["-fcoverage-mapping"] = true,
            ["-fsanitize-cfi-icall-generalize-pointers"] = true,
            ["-include"] = true,
            ["-fvisibility-from-dllstorageclass"] = true,
            ["-fexperimental-new-constant-interpreter"] = true,
            ["-fno-color-diagnostics"] = true,
            ["-fdata-sections"] = true,
            ["-v"] = true,
            ["-fpch-codegen"] = true,
            ["-o"] = true,
            ["-forder-file-instrumentation"] = true,
            ["-print-runtime-dir"] = true,
            ["-fsycl"] = true,
            ["-Xcuda-ptxas"] = true,
            ["-trigraphs"] = true,
            ["-fuse-line-directives"] = true,
            ["-mnvj"] = true,
            ["-fseh-exceptions"] = true,
            ["-fdelayed-template-parsing"] = true,
            ["-fobjc-disable-direct-methods-for-testing"] = true,
            ["-P"] = true,
            ["--config"] = true,
            ["-fsanitize-memory-track-origins"] = true,
            ["-mlong-double-128"] = true,
            ["-extract-api"] = true,
            ["-fintegrated-as"] = true,
            ["-fgpu-rdc"] = true,
            ["-mfp64"] = true,
            ["-fdiagnostics-show-template-tree"] = true,
            ["-fno-spell-checking"] = true,
            ["-ffixed-x29"] = true,
            ["-fdollars-in-identifiers"] = true,
            ["-fno-show-column"] = true,
            ["-mrtd"] = true,
            ["-ffinite-loops"] = true,
            ["-fno-fixed-point"] = true,
            ["-gcodeview"] = true,
            ["-fmodules-decluse"] = true,
            ["-fstack-protector"] = true,
            ["-fno-lto"] = true,
            ["-fno-pseudo-probe-for-profiling"] = true,
            ["-fno-finite-loops"] = true,
            ["-fforce-enable-int128"] = true,
            ["-fopenmp-extensions"] = true,
            ["-fno-diagnostics-fixit-info"] = true,
            ["-fno-xray-function-index"] = true,
            ["-fmemory-profile"] = true,
            ["-fno-gpu-allow-device-init"] = true,
            ["-fkeep-static-consts"] = true,
            ["-fcall-saved-x12"] = true,
            ["-fvisibility-inlines-hidden-static-local-var"] = true,
            ["-mlong-double-80"] = true,
            ["-mbackchain"] = true,
            ["-fno-sanitize-trap"] = true,
            ["-enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang"] = true,
            ["-fgpu-defer-diag"] = true,
            ["-ffixed-x20"] = true,
            ["-mcmse"] = true,
            ["-MMD"] = true,
            ["-Xopenmp-target"] = true,
            ["-nohipwrapperinc"] = true,
            ["-fprofile-generate"] = true,
            ["-faddrsig"] = true,
            ["-fno-gnu-inline-asm"] = true,
            ["-fpseudo-probe-for-profiling"] = true,
            ["-freg-struct-return"] = true,
            ["-fno-sanitize-memory-track-origins"] = true,
            ["-finstrument-functions-after-inlining"] = true,
            ["-fsanitize-cfi-cross-dso"] = true,
            ["-fno-addrsig"] = true,
            ["-fxray-link-deps"] = true,
            ["-fxray-ignore-loops"] = true,
            ["-frwpi"] = true,
            ["-fcommon"] = true,
            ["-cl-uniform-work-group-size"] = true,
            ["-fgpu-sanitize"] = true,
            ["-ftrapv"] = true,
            ["-fno-crash-diagnostics"] = true,
            ["-isysroot"] = true,
            ["-fsanitize-address-globals-dead-stripping"] = true,
            ["-ffixed-x14"] = true,
            ["-fstack-usage"] = true,
            ["--hip-link"] = true,
            ["-mno-nvs"] = true,
            ["--cuda-host-only"] = true,
            ["-mtls-direct-seg-refs"] = true,
            ["-fcall-saved-x15"] = true,
            ["-fcall-saved-x10"] = true,
            ["-fimplicit-module-maps"] = true,
            ["-ffixed-x16"] = true,
            ["-cl-kernel-arg-info"] = true,
            ["-fcall-saved-x11"] = true,
            ["-mabicalls"] = true,
            ["-fsplit-lto-unit"] = true,
            ["-fno-direct-access-external-data"] = true,
            ["-fno-global-isel"] = true,
            ["-print-search-dirs"] = true,
            ["-isystem"] = true,
            ["-print-supported-cpus"] = true,
            ["-fdiagnostics-parseable-fixits"] = true,
            ["-ffixed-r9"] = true,
            ["--cuda-noopt-device-debug"] = true,
            ["-ffixed-x5"] = true,
            ["-feliminate-unused-debug-types"] = true,
            ["-fstack-clash-protection"] = true,
            ["-fdiagnostics-print-source-range-info"] = true,
            ["-fno-signed-zeros"] = true,
            ["-mno-hvx"] = true,
            ["-ffixed-x6"] = true,
            ["-fno-elide-type"] = true,
            ["-fignore-exceptions"] = true,
            ["-fjump-tables"] = true,
            ["-fno-offload-lto"] = true,
            ["-gdwarf-4"] = true,
            ["-fno-zero-initialized-in-bss"] = true,
            ["-fno-builtin"] = true,
            ["-ffixed-x2"] = true,
            ["-ftrigraphs"] = true,
            ["-verify-pch"] = true,
            ["-ffixed-x25"] = true,
            ["-ffixed-d7"] = true,
            ["-gembed-source"] = true,
            ["-fno-legacy-pass-manager"] = true,
            ["-ffine-grained-bitfield-accesses"] = true,
            ["-mno-extern-sdata"] = true,
            ["-fno-show-source-location"] = true,
            ["-gmodules"] = true,
            ["-print-multiarch"] = true,
            ["-fno-profile-instr-use"] = true,
            ["-mmt"] = true,
            ["-mincremental-linker-compatible"] = true,
            ["-gno-embed-source"] = true,
            ["-Qunused-arguments"] = true,
            ["-fbuiltin-module-map"] = true,
            ["-help"] = true,
            ["-Xcuda-fatbinary"] = true,
            ["-ffixed-x9"] = true,
            ["-Tbss"] = true,
            ["-mno-outline-atomics"] = true,
            ["-fsplit-machine-functions"] = true,
            ["-CC"] = true,
            ["-Xassembler"] = true,
            ["-munsafe-fp-atomics"] = true,
            ["-x"] = true,
            ["-fopenmp-target-debug"] = true,
            ["-fprebuilt-implicit-modules"] = true,
            ["-Xclang"] = true,
            ["-w"] = true,
            ["-fembed-bitcode"] = true,
            ["--version"] = true,
            ["-fmodules-validate-system-headers"] = true,
            ["-fno-pch-codegen"] = true,
            ["-fno-sanitize-memory-param-retval"] = true,
            ["-T"] = true,
            ["-undef"] = true,
            ["-mno-unaligned-access"] = true,
            ["-print-targets"] = true,
            ["-mno-nvj"] = true,
            ["-mlvi-hardening"] = true,
            ["-static-openmp"] = true,
            ["-static-libsan"] = true,
            ["-fintegrated-cc1"] = true,
            ["-objcmt-ns-nonatomic-iosonly"] = true,
            ["-emit-interface-stubs"] = true,
            ["-save-temps"] = true,
            ["-Qy"] = true,
            ["-foffload-lto"] = true,
            ["-mno-msa"] = true,
            ["-mno-relax"] = true,
            ["-MD"] = true,
            ["-fno-aapcs-bitfield-width"] = true,
            ["-fmodules-disable-diagnostic-validation"] = true,
            ["-ffixed-x31"] = true,
            ["-fno-profile-generate"] = true,
            ["-mlvi-cfi"] = true,
            ["-objcmt-returns-innerpointer-property"] = true,
            ["-fsanitize-cfi-canonical-jump-tables"] = true,
            ["-serialize-diagnostics"] = true,
            ["-mignore-xcoff-visibility"] = true,
            ["-cl-mad-enable"] = true,
            ["-ffixed-d4"] = true,
            ["-fno-sanitize-ignorelist"] = true,
            ["-objcmt-migrate-all"] = true,
            ["-fobjc-encode-cxx-class-template-spec"] = true,
            ["-objcmt-migrate-protocol-conformance"] = true,
            ["-nogpulib"] = true,
            ["-fmodules-validate-once-per-build-session"] = true,
            ["-objcmt-allowlist-dir-path"] = true,
            ["-mno-tls-direct-seg-refs"] = true,
            ["-z"] = true,
            ["--gpu-bundle-output"] = true,
            ["-mno-movt"] = true,
            ["-fno-integrated-as"] = true,
            ["-mstack-arg-probe"] = true,
            ["--verify-debug-info"] = true,
            ["-mqdsp6-compat"] = true,
            ["-fno-threadsafe-statics"] = true,
            ["-flto"] = true,
            ["-fno-standalone-debug"] = true,
            ["-fno-integrated-cc1"] = true,
            ["-ffixed-x26"] = true,
            ["-fno-eliminate-unused-debug-types"] = true,
            ["-fexceptions"] = true,
            ["-ffixed-x19"] = true,
            ["-mfentry"] = true,
            ["-isystem-after"] = true,
            ["-mno-implicit-float"] = true,
            ["-funique-basic-block-section-names"] = true,
            ["-pthread"] = true
        }
    }
}