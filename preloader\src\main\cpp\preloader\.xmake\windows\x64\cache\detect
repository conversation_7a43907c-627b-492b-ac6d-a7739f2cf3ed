{
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolcxx"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_program_utils.binary.deplibs"] = {
        objdump = [[C:\msys64\usr\bin\objdump.exe]],
        ["llvm-objdump"] = false
    },
    ["lib.detect.has_flags"] = {
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-std=c++20"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-DNDEBUG"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-Oz"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fvisibility-inlines-hidden"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_sh__-shared -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb_-fPIC"] = true
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolsh"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    find_program_fetch_package_xmake = {
        cmake = false,
        ninja = false
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["--disable-high-entropy-va"] = true,
            ["--fatal-warnings"] = true,
            ["--demangle"] = true,
            ["--version"] = true,
            ["-dn"] = true,
            ["--large-address-aware"] = true,
            ["--exclude-all-symbols"] = true,
            ["-o"] = true,
            ["--no-fatal-warnings"] = true,
            ["--verbose"] = true,
            ["--allow-multiple-definition"] = true,
            ["--disable-dynamicbase"] = true,
            ["--strip-all"] = true,
            ["--no-demangle"] = true,
            ["--Bstatic"] = true,
            ["--help"] = true,
            ["--disable-tsaware"] = true,
            ["-L"] = true,
            ["--disable-no-seh"] = true,
            ["--no-seh"] = true,
            ["--tsaware"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["--no-insert-timestamp"] = true,
            ["-S"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["--nxcompat"] = true,
            ["--export-all-symbols"] = true,
            ["--Bdynamic"] = true,
            ["--gc-sections"] = true,
            ["--shared"] = true,
            ["-dy"] = true,
            ["-m"] = true,
            ["-static"] = true,
            ["-l"] = true,
            ["--appcontainer"] = true,
            ["--enable-auto-import"] = true,
            ["-s"] = true,
            ["--whole-archive"] = true,
            ["--high-entropy-va"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["-v"] = true,
            ["--no-allow-multiple-definition"] = true,
            ["--kill-at"] = true,
            ["--strip-debug"] = true,
            ["--dynamicbase"] = true,
            ["--no-whole-archive"] = true,
            ["--disable-auto-import"] = true,
            ["--no-dynamicbase"] = true,
            ["--insert-timestamp"] = true,
            ["--no-gc-sections"] = true,
            ["--disable-nxcompat"] = true
        }
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
            sdkver = "21",
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
            ndkver = 25,
            cross = "arm-linux-androideabi-",
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]]
        }
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-c"] = true,
            ["-fzvector"] = true,
            ["-mno-restrict-it"] = true,
            ["-gcodeview-ghash"] = true,
            ["-menable-unsafe-fp-math"] = true,
            ["-E"] = true,
            ["-ftime-trace"] = true,
            ["-fcf-protection"] = true,
            ["-fexperimental-strict-floating-point"] = true,
            ["-fapple-link-rtlib"] = true,
            ["--migrate"] = true,
            ["-fcs-profile-generate"] = true,
            ["-fsanitize-cfi-canonical-jump-tables"] = true,
            ["-freroll-loops"] = true,
            ["-mqdsp6-compat"] = true,
            ["-fprofile-generate"] = true,
            ["-fcxx-modules"] = true,
            ["-fpch-validate-input-files-content"] = true,
            ["-fno-assume-sane-operator-new"] = true,
            ["-mbackchain"] = true,
            ["-F"] = true,
            ["-fshort-enums"] = true,
            ["-nogpulib"] = true,
            ["-fno-new-infallible"] = true,
            ["-fvisibility-inlines-hidden"] = true,
            ["-fno-profile-instr-use"] = true,
            ["-nogpuinc"] = true,
            ["-traditional-cpp"] = true,
            ["-save-temps"] = true,
            ["-fmodules-validate-system-headers"] = true,
            ["-objcmt-migrate-annotation"] = true,
            ["-C"] = true,
            ["-fmerge-all-constants"] = true,
            ["-mno-code-object-v3"] = true,
            ["-fpascal-strings"] = true,
            ["-MJ"] = true,
            ["-fintegrated-as"] = true,
            ["-gdwarf32"] = true,
            ["-fno-preserve-as-comments"] = true,
            ["-ffixed-x17"] = true,
            ["--help-hidden"] = true,
            ["-I-"] = true,
            ["-ffast-math"] = true,
            ["-MQ"] = true,
            ["-faligned-allocation"] = true,
            ["-fverbose-asm"] = true,
            ["-fno-threadsafe-statics"] = true,
            ["-ffixed-d2"] = true,
            ["-momit-leaf-frame-pointer"] = true,
            ["-menable-experimental-extensions"] = true,
            ["-D"] = true,
            ["-fwritable-strings"] = true,
            ["-fno-finite-loops"] = true,
            ["-fsanitize-thread-memory-access"] = true,
            ["-fno-declspec"] = true,
            ["-Tdata"] = true,
            ["-mstack-arg-probe"] = true,
            ["-fobjc-disable-direct-methods-for-testing"] = true,
            ["-fforce-emit-vtables"] = true,
            ["-Ttext"] = true,
            ["-fno-sanitize-stats"] = true,
            ["--analyzer-output"] = true,
            ["-fno-discard-value-names"] = true,
            ["-mnvj"] = true,
            ["-fno-sanitize-address-use-after-scope"] = true,
            ["-g"] = true,
            ["-fsanitize-thread-func-entry-exit"] = true,
            ["-fsanitize-cfi-icall-generalize-pointers"] = true,
            ["-fno-operator-names"] = true,
            ["-I"] = true,
            ["-objcmt-migrate-all"] = true,
            ["--cuda-device-only"] = true,
            ["-fpch-debuginfo"] = true,
            ["-fvisibility-global-new-delete-hidden"] = true,
            ["-fcuda-short-ptr"] = true,
            ["-fsanitize-hwaddress-experimental-aliasing"] = true,
            ["-idirafter"] = true,
            ["-ffixed-d6"] = true,
            ["-fno-sanitize-address-outline-instrumentation"] = true,
            ["-ffixed-x29"] = true,
            ["-fno-addrsig"] = true,
            ["-funique-basic-block-section-names"] = true,
            ["-mrelax"] = true,
            ["-fno-unroll-loops"] = true,
            ["-fmemory-profile"] = true,
            ["-mno-hvx"] = true,
            ["-mno-fix-cortex-a53-835769"] = true,
            ["-mno-outline"] = true,
            ["-fvisibility-ms-compat"] = true,
            ["-help"] = true,
            ["-fminimize-whitespace"] = true,
            ["-MG"] = true,
            ["-fsanitize-address-globals-dead-stripping"] = true,
            ["-fbuiltin-module-map"] = true,
            ["-mnocrc"] = true,
            ["-fno-openmp-extensions"] = true,
            ["-fdwarf-exceptions"] = true,
            ["-MP"] = true,
            ["-fno-plt"] = true,
            ["-cl-finite-math-only"] = true,
            ["-shared-libsan"] = true,
            ["-print-ivar-layout"] = true,
            ["-objcmt-migrate-readonly-property"] = true,
            ["-G"] = true,
            ["-fopenmp"] = true,
            ["-fregister-global-dtors-with-atexit"] = true,
            ["-fcall-saved-x8"] = true,
            ["-fpch-codegen"] = true,
            ["-ffixed-x30"] = true,
            ["-fdiagnostics-show-hotness"] = true,
            ["-freg-struct-return"] = true,
            ["-mno-relax"] = true,
            ["-fprotect-parens"] = true,
            ["-mlong-double-80"] = true,
            ["-mmadd4"] = true,
            ["-fstack-clash-protection"] = true,
            ["-fsplit-stack"] = true,
            ["-fapple-pragma-pack"] = true,
            ["-fsycl"] = true,
            ["-mno-hvx-qfloat"] = true,
            ["-mno-neg-immediates"] = true,
            ["-mfentry"] = true,
            ["-gdwarf-3"] = true,
            ["-extract-api"] = true,
            ["-undef"] = true,
            ["-fsanitize-stats"] = true,
            ["-mstackrealign"] = true,
            ["-ffinite-loops"] = true,
            ["-cl-no-stdinc"] = true,
            ["-mcrc"] = true,
            ["-fsanitize-address-use-after-scope"] = true,
            ["-gdwarf"] = true,
            ["-fapplication-extension"] = true,
            ["-fno-sanitize-cfi-cross-dso"] = true,
            ["--version"] = true,
            ["-fprebuilt-implicit-modules"] = true,
            ["-fobjc-arc"] = true,
            ["-fno-delete-null-pointer-checks"] = true,
            ["-include"] = true,
            ["-msoft-float"] = true,
            ["-mno-tls-direct-seg-refs"] = true,
            ["-fno-debug-macro"] = true,
            ["-nostdinc"] = true,
            ["-dependency-file"] = true,
            ["--analyze"] = true,
            ["--start-no-unused-arguments"] = true,
            ["-mllvm"] = true,
            ["-fno-fine-grained-bitfield-accesses"] = true,
            ["-faapcs-bitfield-width"] = true,
            ["-verify-pch"] = true,
            ["-fno-hip-new-launch-api"] = true,
            ["-fcall-saved-x15"] = true,
            ["-fnew-infallible"] = true,
            ["--end-no-unused-arguments"] = true,
            ["-fno-digraphs"] = true,
            ["-mmark-bti-property"] = true,
            ["-ffixed-x31"] = true,
            ["-fno-sanitize-thread-atomics"] = true,
            ["-iwithprefix"] = true,
            ["-fno-pch-debuginfo"] = true,
            ["-ffixed-x22"] = true,
            ["-MMD"] = true,
            ["-ffixed-x21"] = true,
            ["-ffixed-x6"] = true,
            ["-mno-gpopt"] = true,
            ["--precompile"] = true,
            ["-mlong-calls"] = true,
            ["--no-cuda-version-check"] = true,
            ["-ffixed-x12"] = true,
            ["-finstrument-functions-after-inlining"] = true,
            ["-mno-implicit-float"] = true,
            ["-ffixed-x19"] = true,
            ["-emit-ast"] = true,
            ["-ffixed-x14"] = true,
            ["-fsanitize-memory-track-origins"] = true,
            ["-fno-builtin"] = true,
            ["-objcmt-migrate-protocol-conformance"] = true,
            ["-munsafe-fp-atomics"] = true,
            ["-mno-embedded-data"] = true,
            ["-objcmt-migrate-readwrite-property"] = true,
            ["-rpath"] = true,
            ["-print-runtime-dir"] = true,
            ["-fcall-saved-x9"] = true,
            ["-fno-double-square-bracket-attributes"] = true,
            ["-MV"] = true,
            ["-mno-unaligned-access"] = true,
            ["-pipe"] = true,
            ["-ffixed-x7"] = true,
            ["-emit-llvm"] = true,
            ["-ffixed-x20"] = true,
            ["-static-openmp"] = true,
            ["-mwavefrontsize64"] = true,
            ["-fno-sanitize-ignorelist"] = true,
            ["-fgpu-sanitize"] = true,
            ["-arch"] = true,
            ["-fno-show-column"] = true,
            ["-nobuiltininc"] = true,
            ["-fdelete-null-pointer-checks"] = true,
            ["-mpacked-stack"] = true,
            ["-ffixed-d0"] = true,
            ["-fprofile-instr-generate"] = true,
            ["-cl-strict-aliasing"] = true,
            ["-fno-aapcs-bitfield-width"] = true,
            ["-ffixed-d3"] = true,
            ["-ffixed-x10"] = true,
            ["-mno-cumode"] = true,
            ["-MM"] = true,
            ["-relocatable-pch"] = true,
            ["-fxray-always-emit-typedevents"] = true,
            ["-fdollars-in-identifiers"] = true,
            ["-fwhole-program-vtables"] = true,
            ["-fsystem-module"] = true,
            ["-mno-lvi-hardening"] = true,
            ["-fno-strict-float-cast-overflow"] = true,
            ["-fno-cuda-approx-transcendentals"] = true,
            ["-fsanitize-cfi-cross-dso"] = true,
            ["-ffixed-x5"] = true,
            ["-fobjc-encode-cxx-class-template-spec"] = true,
            ["-emit-merged-ifs"] = true,
            ["-mtgsplit"] = true,
            ["-fno-common"] = true,
            ["-objcmt-migrate-property"] = true,
            ["-ffixed-x11"] = true,
            ["-fno-eliminate-unused-debug-types"] = true,
            ["-fdirect-access-external-data"] = true,
            ["-fno-complete-member-pointers"] = true,
            ["-mhvx-qfloat"] = true,
            ["-fpch-instantiate-templates"] = true,
            ["-rewrite-objc"] = true,
            ["-fno-direct-access-external-data"] = true,
            ["-finline-functions"] = true,
            ["-index-header-map"] = true,
            ["-cl-fast-relaxed-math"] = true,
            ["-fstrict-enums"] = true,
            ["--verify-debug-info"] = true,
            ["-fcall-saved-x13"] = true,
            ["-fasync-exceptions"] = true,
            ["-gdwarf-5"] = true,
            ["-emit-interface-stubs"] = true,
            ["-dependency-dot"] = true,
            ["-fno-sanitize-memory-param-retval"] = true,
            ["-ftrigraphs"] = true,
            ["-arcmt-migrate-emit-errors"] = true,
            ["-fdiagnostics-show-note-include-stack"] = true,
            ["-ffixed-x8"] = true,
            ["-ffixed-x24"] = true,
            ["-fansi-escape-codes"] = true,
            ["-fjump-tables"] = true,
            ["-ffixed-d1"] = true,
            ["-fno-stack-protector"] = true,
            ["-fmodules-validate-once-per-build-session"] = true,
            ["-mtls-direct-seg-refs"] = true,
            ["-fgnu-runtime"] = true,
            ["-mskip-rax-setup"] = true,
            ["-gno-inline-line-tables"] = true,
            ["-fdata-sections"] = true,
            ["-cl-single-precision-constant"] = true,
            ["-fdiagnostics-show-option"] = true,
            ["-mlocal-sdata"] = true,
            ["-mbranches-within-32B-boundaries"] = true,
            ["-Tbss"] = true,
            ["-fxray-always-emit-customevents"] = true,
            ["-fcall-saved-x10"] = true,
            ["-forder-file-instrumentation"] = true,
            ["-T"] = true,
            ["-fvectorize"] = true,
            ["--cuda-host-only"] = true,
            ["-ffixed-a5"] = true,
            ["-fxray-ignore-loops"] = true,
            ["-mno-memops"] = true,
            ["-mfp32"] = true,
            ["-objcmt-migrate-subscripting"] = true,
            ["-fno-sycl"] = true,
            ["-fsjlj-exceptions"] = true,
            ["-fno-signed-char"] = true,
            ["-fdebug-info-for-profiling"] = true,
            ["-S"] = true,
            ["-fglobal-isel"] = true,
            ["-ffixed-d7"] = true,
            ["-fconvergent-functions"] = true,
            ["-gline-directives-only"] = true,
            ["-frelaxed-template-template-args"] = true,
            ["-objcmt-migrate-designated-init"] = true,
            ["-cl-opt-disable"] = true,
            ["-mno-seses"] = true,
            ["-cl-unsafe-math-optimizations"] = true,
            ["-iprefix"] = true,
            ["-fno-unique-section-names"] = true,
            ["-mno-local-sdata"] = true,
            ["-mcumode"] = true,
            ["-fno-access-control"] = true,
            ["-print-rocm-search-dirs"] = true,
            ["-cl-kernel-arg-info"] = true,
            ["-fno-hip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-print-multiarch"] = true,
            ["-finline-hint-functions"] = true,
            ["-fstack-protector"] = true,
            ["-fno-sanitize-address-use-odr-indicator"] = true,
            ["-fcomplete-member-pointers"] = true,
            ["-fdebug-ranges-base-address"] = true,
            ["-fno-rtlib-add-rpath"] = true,
            ["-mmsa"] = true,
            ["-mfp64"] = true,
            ["-fno-sanitize-memory-use-after-dtor"] = true,
            ["-fmodules-disable-diagnostic-validation"] = true,
            ["-nohipwrapperinc"] = true,
            ["-x"] = true,
            ["-mseses"] = true,
            ["-pthread"] = true,
            ["-ffunction-sections"] = true,
            ["-Xpreprocessor"] = true,
            ["-iframeworkwithsysroot"] = true,
            ["-U"] = true,
            ["-frtlib-add-rpath"] = true,
            ["-fcolor-diagnostics"] = true,
            ["-fno-profile-instr-generate"] = true,
            ["-fno-elide-constructors"] = true,
            ["-fblocks"] = true,
            ["-fno-sanitize-trap"] = true,
            ["-objcmt-migrate-ns-macros"] = true,
            ["-dD"] = true,
            ["-fno-split-stack"] = true,
            ["-mno-global-merge"] = true,
            ["-fno-gnu-inline-asm"] = true,
            ["-iwithprefixbefore"] = true,
            ["-frwpi"] = true,
            ["-mhvx-ieee-fp"] = true,
            ["-isysroot"] = true,
            ["-ffixed-x9"] = true,
            ["-fembed-bitcode"] = true,
            ["--gpu-bundle-output"] = true,
            ["-fhip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-z"] = true,
            ["-mno-stack-arg-probe"] = true,
            ["-cl-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fno-legacy-pass-manager"] = true,
            ["-fno-lto"] = true,
            ["-mno-incremental-linker-compatible"] = true,
            ["-fno-offload-lto"] = true,
            ["-print-search-dirs"] = true,
            ["-fsave-optimization-record"] = true,
            ["-cl-mad-enable"] = true,
            ["-fborland-extensions"] = true,
            ["-fno-jump-tables"] = true,
            ["-cxx-isystem"] = true,
            ["-fno-split-machine-functions"] = true,
            ["-mlvi-cfi"] = true,
            ["-fno-stack-clash-protection"] = true,
            ["-ffixed-x16"] = true,
            ["-gdwarf-2"] = true,
            ["-mpackets"] = true,
            ["-fdebug-types-section"] = true,
            ["-funique-internal-linkage-names"] = true,
            ["-msvr4-struct-return"] = true,
            ["-fmodules-decluse"] = true,
            ["-fdiscard-value-names"] = true,
            ["-fms-extensions"] = true,
            ["-fexperimental-new-constant-interpreter"] = true,
            ["-mno-ms-bitfields"] = true,
            ["-fcall-saved-x14"] = true,
            ["-mno-hvx-ieee-fp"] = true,
            ["-fno-global-isel"] = true,
            ["-fno-use-init-array"] = true,
            ["-ffixed-x18"] = true,
            ["-fno-elide-type"] = true,
            ["-cl-uniform-work-group-size"] = true,
            ["-mthread-model"] = true,
            ["-ffixed-a1"] = true,
            ["-fsanitize-address-poison-custom-array-cookie"] = true,
            ["-fno-use-cxa-atexit"] = true,
            ["-mfix-cmse-cve-2021-35465"] = true,
            ["-fno-fixed-point"] = true,
            ["-flegacy-pass-manager"] = true,
            ["-mms-bitfields"] = true,
            ["-fgpu-allow-device-init"] = true,
            ["-w"] = true,
            ["-fdelayed-template-parsing"] = true,
            ["-Xclang"] = true,
            ["-fgpu-rdc"] = true,
            ["-mrtd"] = true,
            ["-fno-coverage-mapping"] = true,
            ["-fno-force-enable-int128"] = true,
            ["-femulated-tls"] = true,
            ["-fvisibility-from-dllstorageclass"] = true,
            ["-mignore-xcoff-visibility"] = true,
            ["-fxray-instrument"] = true,
            ["-Xassembler"] = true,
            ["-mno-lvi-cfi"] = true,
            ["-Qn"] = true,
            ["-meabi"] = true,
            ["-isystem-after"] = true,
            ["-mmt"] = true,
            ["-mrecord-mcount"] = true,
            ["-objcmt-migrate-instancetype"] = true,
            ["-fpseudo-probe-for-profiling"] = true,
            ["-enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang"] = true,
            ["-mcode-object-v3"] = true,
            ["-fstrict-float-cast-overflow"] = true,
            ["-membedded-data"] = true,
            ["-mamdgpu-ieee"] = true,
            ["--cuda-path-ignore-env"] = true,
            ["-fno-keep-static-consts"] = true,
            ["-mno-outline-atomics"] = true,
            ["-MT"] = true,
            ["-Xopenmp-target"] = true,
            ["-save-stats"] = true,
            ["-fcall-saved-x18"] = true,
            ["-gno-embed-source"] = true,
            ["-ffixed-x15"] = true,
            ["-fvisibility-inlines-hidden-static-local-var"] = true,
            ["-gcodeview"] = true,
            ["-ffixed-x3"] = true,
            ["-mno-save-restore"] = true,
            ["-gdwarf-4"] = true,
            ["-fno-sanitize-cfi-canonical-jump-tables"] = true,
            ["-MF"] = true,
            ["-mibt-seal"] = true,
            ["-mno-extern-sdata"] = true,
            ["-ffixed-x27"] = true,
            ["-mfix-cortex-a53-835769"] = true,
            ["-fsized-deallocation"] = true,
            ["-print-targets"] = true,
            ["-fcommon"] = true,
            ["-emit-module"] = true,
            ["-mrestrict-it"] = true,
            ["-fstack-size-section"] = true,
            ["-mlvi-hardening"] = true,
            ["-gembed-source"] = true,
            ["-fno-constant-cfstrings"] = true,
            ["-finstrument-functions"] = true,
            ["-iwithsysroot"] = true,
            ["-objcmt-atomic-property"] = true,
            ["-fno-exceptions"] = true,
            ["-fgnu-keywords"] = true,
            ["-static-libsan"] = true,
            ["-ffixed-a4"] = true,
            ["-fsplit-machine-functions"] = true,
            ["-mlong-double-128"] = true,
            ["-fmodules-search-all"] = true,
            ["-ffixed-x1"] = true,
            ["-fembed-bitcode-marker"] = true,
            ["-M"] = true,
            ["-Xanalyzer"] = true,
            ["-fobjc-weak"] = true,
            ["-fopenmp-simd"] = true,
            ["-objcmt-allowlist-dir-path"] = true,
            ["-fcxx-exceptions"] = true,
            ["-foffload-lto"] = true,
            ["-dM"] = true,
            ["-fdebug-macro"] = true,
            ["-pg"] = true,
            ["-fms-compatibility"] = true,
            ["-fno-temp-file"] = true,
            ["-ibuiltininc"] = true,
            ["--emit-static-lib"] = true,
            ["-mno-nvj"] = true,
            ["-ffixed-a3"] = true,
            ["-gdwarf64"] = true,
            ["-ffixed-x25"] = true,
            ["-objcmt-ns-nonatomic-iosonly"] = true,
            ["-cl-no-signed-zeros"] = true,
            ["-fdiagnostics-show-template-tree"] = true,
            ["-fxl-pragma-pack"] = true,
            ["-fno-diagnostics-fixit-info"] = true,
            ["-Wdeprecated"] = true,
            ["-mno-movt"] = true,
            ["-fsanitize-thread-atomics"] = true,
            ["-isystem"] = true,
            ["-mno-msa"] = true,
            ["-fkeep-static-consts"] = true,
            ["--config"] = true,
            ["-fno-spell-checking"] = true,
            ["-ffixed-a0"] = true,
            ["-Qy"] = true,
            ["-fstrict-vtable-pointers"] = true,
            ["-objcmt-migrate-literals"] = true,
            ["-fno-dollars-in-identifiers"] = true,
            ["-B"] = true,
            ["-fdeclspec"] = true,
            ["-fobjc-exceptions"] = true,
            ["-fno-standalone-debug"] = true,
            ["-moutline-atomics"] = true,
            ["-fcall-saved-x12"] = true,
            ["-fcuda-approx-transcendentals"] = true,
            ["--hip-link"] = true,
            ["-fdiagnostics-parseable-fixits"] = true,
            ["-fno-register-global-dtors-with-atexit"] = true,
            ["-fshort-wchar"] = true,
            ["-fdiagnostics-print-source-range-info"] = true,
            ["-ffixed-r19"] = true,
            ["-fsanitize-trap"] = true,
            ["-mcmse"] = true,
            ["-fslp-vectorize"] = true,
            ["-fno-color-diagnostics"] = true,
            ["-v"] = true,
            ["-maix-struct-return"] = true,
            ["-fno-show-source-location"] = true,
            ["-gline-tables-only"] = true,
            ["-munaligned-access"] = true,
            ["-femit-all-decls"] = true,
            ["-faddrsig"] = true,
            ["-fignore-exceptions"] = true,
            ["-ffixed-d4"] = true,
            ["-o"] = true,
            ["-ffixed-x26"] = true,
            ["-fno-objc-infer-related-result-type"] = true,
            ["-print-resource-dir"] = true,
            ["-fgpu-defer-diag"] = true,
            ["-fstack-protector-all"] = true,
            ["-fstandalone-debug"] = true,
            ["-pedantic"] = true,
            ["-ffixed-d5"] = true,
            ["-fno-sanitize-thread-func-entry-exit"] = true,
            ["-mlong-double-64"] = true,
            ["--cuda-compile-host-device"] = true,
            ["-ffixed-x4"] = true,
            ["-module-dependency-dir"] = true,
            ["-fwasm-exceptions"] = true,
            ["-fapple-kext"] = true,
            ["--no-gpu-bundle-output"] = true,
            ["-fsplit-lto-unit"] = true,
            ["-mhvx"] = true,
            ["-ffixed-x23"] = true,
            ["-fapprox-func"] = true,
            ["-Xcuda-fatbinary"] = true,
            ["-objcmt-returns-innerpointer-property"] = true,
            ["-fcoverage-mapping"] = true,
            ["-fno-autolink"] = true,
            ["-mnop-mcount"] = true,
            ["-fexceptions"] = true,
            ["-fno-short-wchar"] = true,
            ["-fno-memory-profile"] = true,
            ["-imacros"] = true,
            ["-fno-delayed-template-parsing"] = true,
            ["-fenable-matrix"] = true,
            ["-fmodules-user-build-path"] = true,
            ["-fno-pseudo-probe-for-profiling"] = true,
            ["-fno-crash-diagnostics"] = true,
            ["-miamcu"] = true,
            ["-Xcuda-ptxas"] = true,
            ["-L"] = true,
            ["-fforce-dwarf-frame"] = true,
            ["-ffine-grained-bitfield-accesses"] = true,
            ["-gmodules"] = true,
            ["-fno-visibility-inlines-hidden-static-local-var"] = true,
            ["-fropi"] = true,
            ["-mno-crc"] = true,
            ["-mmemops"] = true,
            ["-fshow-skipped-includes"] = true,
            ["-fno-rtti"] = true,
            ["-module-file-info"] = true,
            ["-ffixed-x13"] = true,
            ["-flto"] = true,
            ["-ffixed-point"] = true,
            ["-fcall-saved-x11"] = true,
            ["-ffixed-a2"] = true,
            ["-ftrapv"] = true,
            ["-rewrite-legacy-objc"] = true,
            ["-fintegrated-cc1"] = true,
            ["-ivfsoverlay"] = true,
            ["-fsanitize-memory-use-after-dtor"] = true,
            ["-fno-signed-zeros"] = true,
            ["-print-effective-triple"] = true,
            ["-ffixed-r9"] = true,
            ["-fopenmp-target-debug"] = true,
            ["-fforce-enable-int128"] = true,
            ["-working-directory"] = true,
            ["-fno-xray-function-index"] = true,
            ["-CC"] = true,
            ["-ffixed-x28"] = true,
            ["-fno-sanitize-memory-track-origins"] = true,
            ["-mexecute-only"] = true,
            ["-arcmt-migrate-report-output"] = true,
            ["-dsym-dir"] = true,
            ["-mglobal-merge"] = true,
            ["-fvalidate-ast-input-files-content"] = true,
            ["-fno-strict-return"] = true,
            ["-fopenmp-target-new-runtime"] = true,
            ["-fno-merge-all-constants"] = true,
            ["-faapcs-bitfield-load"] = true,
            ["-fmodules-strict-decluse"] = true,
            ["-feliminate-unused-debug-types"] = true,
            ["-objcmt-migrate-property-dot-syntax"] = true,
            ["-mno-madd4"] = true,
            ["-fsanitize-address-outline-instrumentation"] = true,
            ["-fallow-editor-placeholders"] = true,
            ["-fuse-line-directives"] = true,
            ["-fprofile-sample-accurate"] = true,
            ["--cuda-noopt-device-debug"] = true,
            ["-mno-bti-at-return-twice"] = true,
            ["-fcoroutines-ts"] = true,
            ["-trigraphs"] = true,
            ["-mno-packets"] = true,
            ["-include-pch"] = true,
            ["-mno-abicalls"] = true,
            ["-ffreestanding"] = true,
            ["-fno-sanitize-thread-memory-access"] = true,
            ["-fopenmp-extensions"] = true,
            ["-fno-cxx-modules"] = true,
            ["-mnvs"] = true,
            ["-fno-integrated-as"] = true,
            ["-fstack-protector-strong"] = true,
            ["-MD"] = true,
            ["-funroll-loops"] = true,
            ["-fmodules"] = true,
            ["-b"] = true,
            ["-mno-fix-cmse-cve-2021-35465"] = true,
            ["-moutline"] = true,
            ["-fno-integrated-cc1"] = true,
            ["-mno-wavefrontsize64"] = true,
            ["-dI"] = true,
            ["-fsigned-char"] = true,
            ["-fno-sanitize-address-poison-custom-array-cookie"] = true,
            ["-mno-long-calls"] = true,
            ["-mabicalls"] = true,
            ["-mno-nvs"] = true,
            ["-malign-double"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-fno-pch-codegen"] = true,
            ["-fmodules-validate-input-files-content"] = true,
            ["-fno-rtti-data"] = true,
            ["-ffixed-x2"] = true,
            ["-finstrument-function-entry-bare"] = true,
            ["-fsanitize-address-use-odr-indicator"] = true,
            ["-fpcc-struct-return"] = true,
            ["-freciprocal-math"] = true,
            ["-time"] = true,
            ["-fseh-exceptions"] = true,
            ["-fno-profile-generate"] = true,
            ["-fstack-usage"] = true,
            ["-fno-zero-initialized-in-bss"] = true,
            ["-print-target-triple"] = true,
            ["-Qunused-arguments"] = true,
            ["-fno-gpu-allow-device-init"] = true,
            ["-fimplicit-module-maps"] = true,
            ["-mrelax-all"] = true,
            ["-fdouble-square-bracket-attributes"] = true,
            ["-fgpu-flush-denormals-to-zero"] = true,
            ["-fhip-new-launch-api"] = true,
            ["-fgnu89-inline"] = true,
            ["-msave-restore"] = true,
            ["-P"] = true,
            ["-mgeneral-regs-only"] = true,
            ["-fno-trigraphs"] = true,
            ["-iquote"] = true,
            ["-fno-sanitize-hwaddress-experimental-aliasing"] = true,
            ["-fdiagnostics-absolute-paths"] = true,
            ["-cl-denorms-are-zero"] = true,
            ["-ffixed-a6"] = true,
            ["-H"] = true,
            ["-fxray-link-deps"] = true,
            ["-print-supported-cpus"] = true,
            ["-fmath-errno"] = true,
            ["-fsplit-dwarf-inlining"] = true,
            ["-fmodules-ts"] = true,
            ["-mno-mt"] = true,
            ["-fdigraphs"] = true,
            ["-fms-hotpatch"] = true,
            ["-fvirtual-function-elimination"] = true,
            ["-mgpopt"] = true,
            ["-mextern-sdata"] = true,
            ["-Xlinker"] = true,
            ["-mincremental-linker-compatible"] = true,
            ["-fno-gpu-defer-diag"] = true,
            ["-fsanitize-memory-param-retval"] = true,
            ["-mno-tgsplit"] = true,
            ["-fobjc-arc-exceptions"] = true,
            ["-mno-execute-only"] = true,
            ["-serialize-diagnostics"] = true
        }
    },
    find_programver = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = "14.0.7"
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolld"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    find_program_fetch_package_system = {
        cmake = false
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    },
    ["find_package_android_armeabi-v7a_fetch_package_xmake"] = {
        ["xmake::fmt_59a17d68dbe340a1a0c7e1c511c99e36_release_10.2.1_external"] = {
            license = "MIT",
            version = "10.2.1",
            static = true,
            links = {
                "fmt"
            },
            linkdirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib]]
            },
            sysincludedirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\include]]
            },
            libfiles = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib\libfmt.a]]
            }
        },
        ["xmake::nlohmann_json_be0d1f3d98814d41bdf16f6957e7a2d6_release_v3.11.3_external"] = {
            license = "MIT",
            sysincludedirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\n\nlohmann_json\v3.11.3\be0d1f3d98814d41bdf16f6957e7a2d6\include]]
            },
            version = "v3.11.3"
        }
    },
    find_program = {
        tar = [[C:\Windows\System32\tar.exe]],
        gzip = [[C:\msys64\usr\bin\gzip.exe]],
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        nim = false,
        clang = false,
        zig = false,
        ["vswhere.exe"] = [[C:\Program Files (x86)\Microsoft Visual Studio\Installer\vswhere.exe]],
        git = [[C:\Program Files\Git\cmd\git.exe]]
    }
}